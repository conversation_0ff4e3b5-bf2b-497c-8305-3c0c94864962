package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/db"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/lib/pq"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run cmd/chessticize/populate-lichess-puzzles/main.go <csv_file> [skip_to_puzzle_id] [batch_size] [--update-evaluations-only]")
		fmt.Println("Example: go run cmd/chessticize/populate-lichess-puzzles/main.go testdata/lichess_db_puzzle_samples.csv")
		fmt.Println("Example: go run cmd/chessticize/populate-lichess-puzzles/main.go testdata/lichess_db_puzzle_samples.csv 00500 100")
		fmt.Println("Example: go run cmd/chessticize/populate-lichess-puzzles/main.go testdata/lichess_db_puzzle_samples.csv \"\" 100 --update-evaluations-only")
		fmt.Println("")
		fmt.Println("Flags:")
		fmt.Println("  --update-evaluations-only  Only update the 3 evaluation columns for existing puzzles")
		os.Exit(1)
	}

	csvFile := os.Args[1]

	// Parse optional skip_to_puzzle_id parameter
	var skipToPuzzleID string
	if len(os.Args) > 2 && os.Args[2] != "" {
		skipToPuzzleID = os.Args[2]
	}

	// Parse optional batch_size parameter (default: 100)
	batchSize := 100
	if len(os.Args) > 3 && os.Args[3] != "--update-evaluations-only" {
		if size, err := strconv.Atoi(os.Args[3]); err == nil && size > 0 {
			batchSize = size
		} else {
			fmt.Printf("Invalid batch size '%s', using default: %d\n", os.Args[3], batchSize)
		}
	}

	// Parse update-evaluations-only flag
	updateEvaluationsOnly := false
	for _, arg := range os.Args[2:] {
		if arg == "--update-evaluations-only" {
			updateEvaluationsOnly = true
			break
		}
	}

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Error loading config: %v", err)
	}

	// Initialize database
	database, err := db.NewConnection(cfg.Database)
	if err != nil {
		log.Fatalf("Error initializing database: %v", err)
	}
	defer func() {
		if err := database.Close(); err != nil {
			log.Printf("Error closing database: %v", err)
		}
	}()

	// Create repository
	lichessPuzzleRepo := repository.NewLichessPuzzleRepository(database.DB)

	// Read and parse CSV file
	file, err := os.Open(csvFile)
	if err != nil {
		log.Fatalf("Error opening CSV file: %v", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			log.Printf("Error closing CSV file: %v", err)
		}
	}()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		log.Fatalf("Error reading CSV: %v", err)
	}

	if len(records) < 2 {
		log.Fatal("CSV file must have at least a header and one data row")
	}

	// Skip header row
	records = records[1:]

	fmt.Printf("Found %d puzzles to import\n", len(records))

	// Skip to specified puzzle ID if provided
	startIndex := 0
	if skipToPuzzleID != "" {
		fmt.Printf("Looking for puzzle ID '%s' to start from...\n", skipToPuzzleID)
		for i, record := range records {
			if len(record) > 0 && record[0] == skipToPuzzleID {
				startIndex = i
				fmt.Printf("Found puzzle ID '%s' at index %d, starting from there\n", skipToPuzzleID, i)
				break
			}
		}
		if startIndex == 0 && len(records) > 0 && records[0][0] != skipToPuzzleID {
			fmt.Printf("Warning: Puzzle ID '%s' not found, starting from beginning\n", skipToPuzzleID)
		}
		records = records[startIndex:]
		fmt.Printf("Processing %d puzzles starting from index %d\n", len(records), startIndex)
	}

	// Process records in batches
	imported := 0
	skipped := 0
	totalRecords := len(records)

	for batchStart := 0; batchStart < totalRecords; batchStart += batchSize {
		batchEnd := batchStart + batchSize
		if batchEnd > totalRecords {
			batchEnd = totalRecords
		}

		batch := records[batchStart:batchEnd]
		batchImported, batchSkipped, lastID := processBatch(lichessPuzzleRepo, batch, batchStart+startIndex, updateEvaluationsOnly)

		imported += batchImported
		skipped += batchSkipped

		// Progress reporting
		processed := batchEnd
		fmt.Printf("Progress: %d/%d (%.1f%%) - Imported: %d, Skipped: %d, Last ID: %s\n",
			processed, totalRecords, float64(processed)/float64(totalRecords)*100, imported, skipped, lastID)
	}

	fmt.Printf("\nSuccessfully imported %d out of %d puzzles (skipped: %d)\n", imported, totalRecords, skipped)
}

// processBatch processes a batch of CSV records and returns the number of imported and skipped puzzles, and the last processed ID
func processBatch(lichessPuzzleRepo repository.ILichessPuzzleRepository, batch [][]string, startIndex int, updateEvaluationsOnly bool) (int, int, string) {
	skipped := 0
	lastID := ""

	var puzzles []models.LichessPuzzle

	for i, record := range batch {
		rowNum := startIndex + i + 2 // +2 for header row and 1-based indexing

		if len(record) < 10 {
			fmt.Printf("Skipping row %d: insufficient columns (expected at least 10, got %d)\n", rowNum, len(record))
			skipped++
			continue
		}

		// Parse CSV fields (original columns)
		puzzleID := record[0]
		fen := record[1]
		movesStr := record[2]
		ratingStr := record[3]
		ratingDeviationStr := record[4]
		popularityStr := record[5]
		nbPlaysStr := record[6]
		themesStr := record[7]
		gameURL := record[8]
		openingTagsStr := record[9]

		// Parse new Stockfish evaluation columns (if available)
		var bestMoveEvalStr, bestMove, positionEvalAfterFirstMoveStr string
		if len(record) >= 13 {
			bestMoveEvalStr = record[10]
			bestMove = record[11]
			positionEvalAfterFirstMoveStr = record[12]
		}

		// Skip empty puzzle IDs
		if puzzleID == "" {
			skipped++
			continue
		}

		// Parse numeric fields
		rating, err := strconv.Atoi(ratingStr)
		if err != nil {
			fmt.Printf("Skipping row %d: invalid rating '%s'\n", rowNum, ratingStr)
			skipped++
			continue
		}

		ratingDeviation, err := strconv.Atoi(ratingDeviationStr)
		if err != nil {
			fmt.Printf("Skipping row %d: invalid rating deviation '%s'\n", rowNum, ratingDeviationStr)
			skipped++
			continue
		}

		popularity, err := strconv.Atoi(popularityStr)
		if err != nil {
			fmt.Printf("Skipping row %d: invalid popularity '%s'\n", rowNum, popularityStr)
			skipped++
			continue
		}

		nbPlays, err := strconv.Atoi(nbPlaysStr)
		if err != nil {
			fmt.Printf("Skipping row %d: invalid nb_plays '%s'\n", rowNum, nbPlaysStr)
			skipped++
			continue
		}

		// Parse moves (space-separated)
		moves := pq.StringArray(strings.Fields(movesStr))
		if len(moves) == 0 {
			fmt.Printf("Skipping row %d: no moves found\n", rowNum)
			skipped++
			continue
		}

		// Parse themes (space-separated)
		themes := pq.StringArray{}
		if themesStr != "" {
			themes = pq.StringArray(strings.Fields(themesStr))
		}

		// Parse opening tags (space-separated)
		openingTags := pq.StringArray{}
		if openingTagsStr != "" {
			openingTags = pq.StringArray(strings.Fields(openingTagsStr))
		}

		// Parse Stockfish evaluation fields
		var bestMoveEval, positionEvalAfterFirstMove *float64
		if bestMoveEvalStr != "" && bestMoveEvalStr != "0" {
			if eval, err := strconv.ParseFloat(bestMoveEvalStr, 64); err == nil {
				bestMoveEval = &eval
			} else {
				fmt.Printf("Warning row %d: invalid best_move_eval '%s', skipping field\n", rowNum, bestMoveEvalStr)
			}
		}
		if positionEvalAfterFirstMoveStr != "" && positionEvalAfterFirstMoveStr != "0" {
			if eval, err := strconv.ParseFloat(positionEvalAfterFirstMoveStr, 64); err == nil {
				positionEvalAfterFirstMove = &eval
			} else {
				fmt.Printf("Warning row %d: invalid position_eval_after_first_move '%s', skipping field\n", rowNum, positionEvalAfterFirstMoveStr)
			}
		}

		// Create LichessPuzzle model
		puzzle := models.LichessPuzzle{
			ID:                         puzzleID,
			FEN:                        fen,
			Moves:                      moves,
			Rating:                     rating,
			RatingDeviation:            ratingDeviation,
			Popularity:                 popularity,
			NbPlays:                    nbPlays,
			Themes:                     themes,
			GameURL:                    gameURL,
			OpeningTags:                openingTags,
			BestMoveEval:               bestMoveEval,
			BestMove:                   bestMove,
			PositionEvalAfterFirstMove: positionEvalAfterFirstMove,
			CreatedAt:                  time.Now(),
			UpdatedAt:                  time.Now(),
		}

		puzzles = append(puzzles, puzzle)
		lastID = puzzle.ID // Track the last puzzle ID processed
	}

	// Use appropriate batch method based on the mode
	if len(puzzles) > 0 {
		var result *repository.BatchSaveResult
		var err error

		if updateEvaluationsOnly {
			fmt.Printf("Updating evaluations only for %d puzzles...\n", len(puzzles))
			result, err = lichessPuzzleRepo.UpdateEvaluationsBatch(context.Background(), puzzles)
		} else {
			result, err = lichessPuzzleRepo.SaveBatch(context.Background(), puzzles)
		}

		if err != nil {
			fmt.Printf("Error in batch operation: %v\n", err)
			// Fall back to individual saves if batch fails completely
			for _, puzzle := range puzzles {
				if saveErr := lichessPuzzleRepo.Save(context.Background(), &puzzle); saveErr != nil {
					fmt.Printf("Error saving puzzle %s: %v\n", puzzle.ID, saveErr)
					skipped++
				}
			}
		} else {
			skipped += result.Skipped
			if result.LastID != "" {
				lastID = result.LastID
			}
			// Print any errors from the batch operation
			for _, batchErr := range result.Errors {
				fmt.Printf("Batch error: %v\n", batchErr)
			}
		}
	}

	return len(puzzles) - skipped, skipped, lastID
}

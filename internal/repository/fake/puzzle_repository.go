package fake

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// fakePuzzle is the GORM model used internally by the fake repository,
// storing array types as strings for SQLite compatibility.
type fakePuzzle struct {
	ID          string `gorm:"primaryKey"`
	GameID      string `gorm:"index"`
	UserID      string `gorm:"index"`
	GameMove    int
	FEN         string
	Moves       string // Comma-separated string
	PrevCP      int
	CP          int
	Theme       models.PuzzleTheme
	UserColor   models.Color
	PuzzleColor models.Color
	Zugzwang    bool
	Tags        string // Comma-separated string
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

// TableName specifies the table name for GORM
func (fakePuzzle) TableName() string {
	return "puzzles" // Match the table name used in migrations/models
}

// PuzzleRepository is a fake implementation of the repository.PuzzleRepository interface
type PuzzleRepository struct {
	db *gorm.DB
}

// NewPuzzleRepository creates a new fake puzzle repository
func NewPuzzleRepository(db *DB) *PuzzleRepository {
	return &PuzzleRepository{
		db: db.DB,
	}
}

// toFakePuzzle converts a models.Puzzle to its fake representation.
func toFakePuzzle(p *models.Puzzle) *fakePuzzle {
	movesStr := ""
	if len(p.Moves) > 0 {
		movesStr = "|" + strings.Join(p.Moves, "|") + "|"
	}
	tagsStr := ""
	if len(p.Tags) > 0 {
		tagsStr = "|" + strings.Join(p.Tags, "|") + "|"
	}

	return &fakePuzzle{
		ID:          p.ID,
		GameID:      p.GameID,
		UserID:      p.UserID,
		GameMove:    p.GameMove,
		FEN:         p.FEN,
		Moves:       movesStr,
		PrevCP:      p.PrevCP,
		CP:          p.CP,
		Theme:       p.Theme,
		UserColor:   p.UserColor,
		PuzzleColor: p.PuzzleColor,
		Zugzwang:    p.Zugzwang,
		Tags:        tagsStr,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}
}

// fromFakePuzzle converts a fakePuzzle back to a models.Puzzle.
func fromFakePuzzle(fp *fakePuzzle) *models.Puzzle {
	var moves pq.StringArray
	if fp.Moves != "" {
		cleanMoves := strings.Trim(fp.Moves, "|")
		if cleanMoves != "" {
			parts := strings.Split(cleanMoves, "|")
			for _, part := range parts {
				if part != "" {
					moves = append(moves, part)
				}
			}
		}
	}

	var tags pq.StringArray
	if fp.Tags != "" {
		cleanTags := strings.Trim(fp.Tags, "|")
		if cleanTags != "" {
			parts := strings.Split(cleanTags, "|")
			for _, part := range parts {
				if part != "" {
					tags = append(tags, part)
				}
			}
		}
	}

	return &models.Puzzle{
		ID:          fp.ID,
		GameID:      fp.GameID,
		UserID:      fp.UserID,
		GameMove:    fp.GameMove,
		FEN:         fp.FEN,
		Moves:       moves,
		PrevCP:      fp.PrevCP,
		CP:          fp.CP,
		Theme:       fp.Theme,
		UserColor:   fp.UserColor,
		PuzzleColor: fp.PuzzleColor,
		Zugzwang:    fp.Zugzwang,
		Tags:        tags,
		CreatedAt:   fp.CreatedAt,
		UpdatedAt:   fp.UpdatedAt,
	}
}

// Create creates a new puzzle
func (r *PuzzleRepository) Create(ctx context.Context, puzzle *models.Puzzle) error {
	if puzzle.ID == "" {
		puzzle.ID = uuid.New().String()
	}
	now := time.Now()
	puzzle.CreatedAt = now
	puzzle.UpdatedAt = now

	fakeP := toFakePuzzle(puzzle)

	return r.db.WithContext(ctx).Create(fakeP).Error
}

// GetByID retrieves a puzzle by ID
func (r *PuzzleRepository) GetByID(ctx context.Context, id string) (*models.Puzzle, error) {
	var fp fakePuzzle
	err := r.db.WithContext(ctx).Model(&fakePuzzle{}).First(&fp, "id = ?", id).Error
	if err != nil {
		// Translate gorm.ErrRecordNotFound if needed, or let it propagate
		return nil, err
	}
	return fromFakePuzzle(&fp), nil
}

// GetByGameID retrieves puzzles for a game
func (r *PuzzleRepository) GetByGameID(ctx context.Context, gameID string) ([]models.Puzzle, error) {
	var fakePuzzles []fakePuzzle
	err := r.db.WithContext(ctx).Model(&fakePuzzle{}).Where("game_id = ?", gameID).Find(&fakePuzzles).Error
	if err != nil {
		return nil, err
	}

	puzzles := make([]models.Puzzle, len(fakePuzzles))
	for i, fp := range fakePuzzles {
		puzzles[i] = *fromFakePuzzle(&fp)
	}
	return puzzles, nil
}

// Update updates a puzzle
func (r *PuzzleRepository) Update(ctx context.Context, puzzle *models.Puzzle) error {
	now := time.Now()
	puzzle.UpdatedAt = now // Ensure UpdatedAt is set on the input model

	fakeP := toFakePuzzle(puzzle)

	// Use GORM's update mechanism with the fakePuzzle struct, omitting CreatedAt.
	// GORM handles mapping struct fields to columns.
	result := r.db.WithContext(ctx).Model(&fakePuzzle{}).Where("id = ?", fakeP.ID).Omit("created_at").Updates(fakeP)

	if result.Error != nil {
		return result.Error
	}

	// GORM v2 doesn't return ErrRecordNotFound on Updates when no rows match.
	// We need to check RowsAffected explicitly if we want to return an error
	// similar to GetByID when the record doesn't exist.
	if result.RowsAffected == 0 {
		// Optionally, check if the record actually exists to differentiate
		// between "not found" and "no changes made"
		var exists int64
		r.db.WithContext(ctx).Model(&fakePuzzle{}).Where("id = ?", fakeP.ID).Count(&exists)
		if exists == 0 {
			return gorm.ErrRecordNotFound // Or a custom error
		}
		// If exists > 0 but RowsAffected == 0, it means no fields were actually updated.
		// This is usually not an error condition for Updates.
	}

	return nil
}

// Delete deletes a puzzle by ID
func (r *PuzzleRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&fakePuzzle{}, "id = ?", id).Error
}

// ListByUserID retrieves a paginated list of puzzles for a specific user,
// optionally filtered by tags and the associated game's time range.
// Returns the list of puzzles and the total count matching the criteria.
// NOTE: This fake implementation mimics the real repository's SQL filtering logic
// using SQLite's LIKE operator.
func (r *PuzzleRepository) ListByUserID(ctx context.Context, userID string, filter repository.PuzzleFilter, offset int, limit int) ([]models.Puzzle, int64, error) {
	var fakePuzzles []fakePuzzle
	var totalCount int64

	// --- Step 1: Query Construction (Filters) ---
	// Base query joining puzzles and games for filtering by game time
	// Use fakePuzzle model for the query
	db := r.db.WithContext(ctx).Model(&fakePuzzle{}).
		Joins("JOIN games ON games.id = puzzles.game_id").
		Where("puzzles.user_id = ?", userID)

	// Omit CompressedPGN from Game to reduce memory usage
	db = db.Omit("Game.CompressedPGN")

	// Apply common filters except for tags (which need special handling for SQLite)
	commonFilter := common.PuzzleFilter(filter)
	commonFilter.Tags = nil // Clear tags to handle them separately

	// Apply common filters
	db = common.ApplyPuzzleFilters(db, commonFilter, false) // false for SQLite

	// Apply tag filters using LIKE and OR, mimicking PostgreSQL's ANY
	if len(filter.Tags) > 0 {
		var tagConditions []string
		var tagValues []interface{}
		for _, tag := range filter.Tags {
			tagConditions = append(tagConditions, "puzzles.tags LIKE ?")
			tagValues = append(tagValues, "%|"+tag+"|%")
		}
		db = db.Where(strings.Join(tagConditions, " OR "), tagValues...)
	}

	// --- Step 2: Get Total Count (After all filters) ---
	// Create a separate count query based on the filtered db
	countDB := db
	// Count directly on the joined and filtered query
	err := countDB.Count(&totalCount).Error // Count based on the fakePuzzle model
	if err != nil {
		return nil, 0, fmt.Errorf("error counting puzzles: %w", err)
	}

	// --- Step 3: Fetch Paginated Results ---
	// Select puzzles.* to avoid ambiguity
	err = db.Select("puzzles.*").Offset(offset).Limit(limit).Order("puzzles.created_at DESC").Find(&fakePuzzles).Error
	if err != nil {
		return nil, 0, fmt.Errorf("error fetching puzzles: %w", err)
	}

	// --- Step 4: Convert to models.Puzzle ---
	puzzles := make([]models.Puzzle, len(fakePuzzles))
	for i, fp := range fakePuzzles {
		puzzles[i] = *fromFakePuzzle(&fp)

		// Load the Game object for each puzzle
		var game models.Game
		err = r.db.WithContext(ctx).
			Model(&models.Game{}).
			Omit("CompressedPGN"). // Omit PGN to reduce memory usage
			Where("id = ?", fp.GameID).
			First(&game).Error

		if err == nil {
			// Only set the Game field if we successfully loaded the game
			puzzles[i].Game = game
		}
	}

	return puzzles, totalCount, nil
}

// GetPuzzleStats retrieves statistics about puzzles for a specific user,
// optionally filtered by the same criteria as ListByUserID.
// If offset and limit are provided, stats will be calculated only for the specified puzzles.
// If grouping is provided, stats will be grouped by time periods.
// Returns an array of PuzzleStats structs, one for each time period (or a single-element array if no grouping is requested).
func (r *PuzzleRepository) GetPuzzleStats(ctx context.Context, userID string, filter repository.PuzzleFilter, offset int, limit int, grouping *common.TimeGrouping) ([]*models.PuzzleStats, error) {
	// Get puzzles matching the filter with pagination
	puzzles, totalCount, err := r.ListByUserID(ctx, userID, filter, offset, limit)
	if err != nil {
		return nil, fmt.Errorf("error fetching puzzles for stats: %w", err)
	}

	// If no grouping is requested, calculate stats for all puzzles
	if grouping == nil || !grouping.IsValid() {
		stats := &models.PuzzleStats{
			TotalCount: totalCount,
		}

		// Calculate stats for all puzzles using common utility
		common.CalculatePuzzleStats(puzzles, stats)

		return []*models.PuzzleStats{stats}, nil
	}

	// Calculate grouped stats using the simpler single-pass approach
	return common.CalculateGroupedPuzzleStats(puzzles, grouping)
}

// GetRandomPuzzles retrieves random user puzzles for practice based on the provided filter
func (r *PuzzleRepository) GetRandomPuzzles(ctx context.Context, userID string, filter common.UserPuzzleFilter, limit int) ([]models.Puzzle, error) {
	// Build query for user's puzzles with difficulty range (using puzzle length as difficulty metric)
	db := r.db.WithContext(ctx).Model(&fakePuzzle{}).Where("user_id = ?", userID)

	// Use puzzle length (number of moves) as difficulty metric
	// For SQLite, we need to count the number of pipe separators in the moves string
	// Since moves are stored as "|move1|move2|", we count pipes and divide by 2
	// But we need to handle the case where moves might be empty
	db = db.Where("(LENGTH(moves) - LENGTH(REPLACE(moves, '|', ''))) / 2 >= ? AND (LENGTH(moves) - LENGTH(REPLACE(moves, '|', ''))) / 2 <= ?", filter.MinMoveLength, filter.MaxMoveLength)

	// Filter by tags if provided (using tags field for user puzzles)
	if len(filter.Tags) > 0 {
		var tagConditions []string
		var tagValues []interface{}
		for _, tag := range filter.Tags {
			tagConditions = append(tagConditions, "tags LIKE ?")
			tagValues = append(tagValues, "%|"+tag+"|%")
		}
		db = db.Where(strings.Join(tagConditions, " OR "), tagValues...)
	}

	// Filter by themes if provided (using theme field for user puzzles)
	if len(filter.Themes) > 0 {
		var themeConditions []string
		var themeValues []interface{}
		for _, theme := range filter.Themes {
			themeConditions = append(themeConditions, "theme = ?")
			themeValues = append(themeValues, theme)
		}
		db = db.Where(strings.Join(themeConditions, " OR "), themeValues...)
	}

	// Filter by game time range if provided
	if filter.GameTimeStart != nil || filter.GameTimeEnd != nil {
		// For fake implementation, we'll simulate this by filtering on created_at
		// In a real implementation, this would join with the games table
		if filter.GameTimeStart != nil {
			db = db.Where("created_at >= ?", filter.GameTimeStart)
		}
		if filter.GameTimeEnd != nil {
			db = db.Where("created_at <= ?", filter.GameTimeEnd)
		}
	}

	// Exclude disliked puzzles
	if len(filter.DislikedIDs) > 0 {
		var excludeConditions []string
		var excludeValues []interface{}
		for _, dislikedID := range filter.DislikedIDs {
			excludeConditions = append(excludeConditions, "id != ?")
			excludeValues = append(excludeValues, dislikedID)
		}
		db = db.Where(strings.Join(excludeConditions, " AND "), excludeValues...)
	}

	var fakePuzzles []fakePuzzle
	err := db.Order("RANDOM()").Limit(limit).Find(&fakePuzzles).Error
	if err != nil {
		return nil, err
	}

	// Convert to models.Puzzle
	puzzles := make([]models.Puzzle, len(fakePuzzles))
	for i, fp := range fakePuzzles {
		puzzles[i] = *fromFakePuzzle(&fp)
	}

	return puzzles, nil
}

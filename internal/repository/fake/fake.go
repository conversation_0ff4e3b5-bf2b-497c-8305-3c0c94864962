package fake

import (
	"log"
	"os"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/glebarez/sqlite"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DB represents an in-memory SQLite database for testing
type DB struct {
	*gorm.DB
}

// NewDB creates a new in-memory SQLite database for testing
func NewDB(t *testing.T) *DB {
	// Configure the logger of gorm
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Warn,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	db, err := gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{
		Logger: newLogger,
	})
	require.NoError(t, err)

	// Enable foreign keys in SQLite
	err = db.Exec("PRAGMA foreign_keys = ON").Error
	require.NoError(t, err)

	// Migrate the test schema using fake models where appropriate
	err = db.AutoMigrate(
		&models.User{},                            // Original model
		&models.Game{},                            // Original model
		&fakePuzzle{},                             // Use fake model for correct schema
		&models.ChessProfile{},                    // Original model
		&fakeTask{},                               // Use fake model for correct schema
		&fakePuzzleQueueEntry{},                   // Use fake model for correct schema
		&models.LearningQueueEntry{},              // Original model
		&models.IdempotencyRecord{},               // Use original model now
		&models.InvitationCode{},                  // Original model
		&models.SessionToken{},                    // Original model
		&fakeEvent{},                              // Use fake model for correct schema
		&models.UserPuzzleStats{},                 // Original model
		&models.UserLichessPuzzleStats{},          // Original model
		&models.UserPuzzleArrowDuelStats{},        // Original model
		&models.UserLichessPuzzleArrowDuelStats{}, // Original model
		&models.LichessPuzzle{},                   // Original model
		&models.UserDailyStats{},                  // Original model
		&models.UserLearningDailyStats{},          // Original model
		&models.UserElo{},                         // Original model
		&models.Sprint{},                          // Original model
		&models.SprintPuzzle{},                    // Original model
		&fakeSprintPuzzleAttempt{},                // Use fake model for correct schema
		&models.EloHistory{},                      // Original model
		&models.UserSprintDailyStats{},            // Original model
	)
	require.NoError(t, err)

	return &DB{DB: db}
}

// Close closes the database connection
func (db *DB) Close() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// Ensure repositories implement interfaces
var _ repository.IUserRepository = (*UserRepository)(nil)
var _ repository.IGameRepository = (*GameRepository)(nil)
var _ repository.IPuzzleRepository = (*PuzzleRepository)(nil)
var _ repository.ILichessPuzzleRepository = (*LichessPuzzleRepository)(nil)
var _ repository.ITaskRepository = (*FakeTaskRepository)(nil)
var _ repository.IPuzzleQueueRepository = (*FakePuzzleQueueRepository)(nil)
var _ repository.IIdempotencyRepository = (*FakeIdempotencyRepository)(nil)
var _ repository.IInvitationCodeRepository = (*FakeInvitationCodeRepository)(nil)
var _ repository.ISessionTokenRepository = (*FakeSessionTokenRepository)(nil)
var _ repository.IEventRepository = (*EventRepository)(nil)
var _ repository.IUserDailyStatsRepository = (*UserDailyStatsRepository)(nil)
var _ repository.IUserPuzzleStatsRepository = (*UserPuzzleStatsRepository)(nil)
var _ repository.IUserLichessPuzzleStatsRepository = (*UserLichessPuzzleStatsRepository)(nil)
var _ repository.IUserPuzzleArrowDuelStatsRepository = (*UserPuzzleArrowDuelStatsRepository)(nil)
var _ repository.IUserLichessPuzzleArrowDuelStatsRepository = (*UserLichessPuzzleArrowDuelStatsRepository)(nil)
var _ repository.IUserEloRepository = (*UserEloRepository)(nil)
var _ repository.ISprintRepository = (*SprintRepository)(nil)
var _ repository.ISprintPuzzleRepository = (*SprintPuzzleRepository)(nil)
var _ repository.ISprintPuzzleAttemptRepository = (*SprintPuzzleAttemptRepository)(nil)
var _ repository.ILearningQueueRepository = (*LearningQueueRepository)(nil)
var _ repository.IUserLearningDailyStatsRepository = (*UserLearningDailyStatsRepository)(nil)
var _ repository.IEloHistoryRepository = (*EloHistoryRepository)(nil)
var _ repository.IUserSprintDailyStatsRepository = (*UserSprintDailyStatsRepository)(nil)

package fake

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
)

// LearningQueueRepository is a fake implementation of repository.ILearningQueueRepository
type LearningQueueRepository struct {
	mu                         sync.RWMutex
	entries                    map[string]*models.LearningQueueEntry // key: userID_puzzleID
	userLearningDailyStatsRepo repository.IUserLearningDailyStatsRepository
}

// NewFakeLearningQueueRepository creates a new fake learning queue repository
func NewFakeLearningQueueRepository(db *DB, userLearningDailyStatsRepo repository.IUserLearningDailyStatsRepository) repository.ILearningQueueRepository {
	return &LearningQueueRepository{
		entries:                    make(map[string]*models.LearningQueueEntry),
		userLearningDailyStatsRepo: userLearningDailyStatsRepo,
	}
}

// AddPuzzlesToQueue adds failed puzzles to the user's learning queue
func (r *LearningQueueRepository) AddPuzzlesToQueue(ctx context.Context, userID string, puzzles []models.LearningQueueEntry) (int, error) {
	r.mu.Lock()
	defer r.mu.Unlock()

	addedCount := 0
	for _, puzzle := range puzzles {
		key := fmt.Sprintf("%s_%s", userID, puzzle.LichessPuzzleID)

		// Check if already exists
		if _, exists := r.entries[key]; exists {
			continue // Skip duplicates
		}

		// Set ID if not set
		if puzzle.ID == "" {
			puzzle.ID = uuid.New().String()
		}

		// Set timestamps
		now := time.Now()
		puzzle.CreatedAt = now
		puzzle.UpdatedAt = now

		// Store the entry
		entryCopy := puzzle
		r.entries[key] = &entryCopy
		addedCount++
	}

	return addedCount, nil
}

// GetDuePuzzles retrieves due puzzles for a user, optionally filtered by attempt type
func (r *LearningQueueRepository) GetDuePuzzles(ctx context.Context, userID string, attemptType *string, limit int) ([]models.LearningQueueItem, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var items []models.LearningQueueItem
	now := time.Now()

	for _, entry := range r.entries {
		if entry.UserID != userID {
			continue
		}

		// Check if due
		if entry.DueAt.After(now) {
			continue
		}

		// Filter by attempt type if specified
		if attemptType != nil && *attemptType != "" && entry.FailedAttemptType != *attemptType {
			continue
		}

		// Create fake puzzle data
		puzzleData := &models.LichessPuzzle{
			ID:     entry.LichessPuzzleID,
			FEN:    "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:  []string{"e2e4", "e7e5"},
			Rating: 1500,
			Themes: []string{"opening"},
		}

		item := models.LearningQueueItem{
			QueueID:            entry.ID,
			LichessPuzzleID:    entry.LichessPuzzleID,
			FailedAttemptType:  entry.FailedAttemptType,
			DueAt:              entry.DueAt,
			AttemptsSinceAdded: entry.AttemptsSinceAdded,
			ConsecutiveCorrect: entry.ConsecutiveCorrect,
			OriginalSprintID:   entry.OriginalSprintID,
			PuzzleData:         puzzleData,
		}

		items = append(items, item)

		// Apply limit
		if len(items) >= limit && limit > 0 {
			break
		}
	}

	return items, nil
}

// UpdateAfterAttempt updates a learning queue entry after an attempt
func (r *LearningQueueRepository) UpdateAfterAttempt(ctx context.Context, userID, lichessPuzzleID string, wasCorrect bool, attemptType string, timeSpent int) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	key := fmt.Sprintf("%s_%s", userID, lichessPuzzleID)
	entry, exists := r.entries[key]
	if !exists {
		// Puzzle not in learning queue, nothing to update
		return nil
	}

	// Update attempt counters
	entry.AttemptsSinceAdded++
	wasMastered := false

	// Apply spaced repetition logic
	if wasCorrect {
		entry.ConsecutiveCorrect++

		// If mastered (5 consecutive correct), remove from queue
		if entry.ConsecutiveCorrect >= 5 {
			wasMastered = true
			delete(r.entries, key)
		} else {
			// Schedule for future review based on consecutive correct count
			switch entry.ConsecutiveCorrect {
			case 1:
				entry.DueAt = time.Now().Add(2 * 24 * time.Hour) // 2 days
			case 2:
				entry.DueAt = time.Now().Add(4 * 24 * time.Hour) // 4 days
			case 3:
				entry.DueAt = time.Now().Add(7 * 24 * time.Hour) // 7 days
			case 4:
				entry.DueAt = time.Now().Add(15 * 24 * time.Hour) // 15 days
			}
			entry.UpdatedAt = time.Now()
		}
	} else {
		// Incorrect attempt - reset consecutive correct and schedule for tomorrow
		entry.ConsecutiveCorrect = 0
		entry.DueAt = time.Now().Add(24 * time.Hour) // Try again tomorrow
		entry.UpdatedAt = time.Now()
	}

	// Track daily stats if repository is available
	if r.userLearningDailyStatsRepo != nil {
		now := time.Now()

		// Track the learning attempt
		err := r.userLearningDailyStatsRepo.IncrementLearningAttempt(ctx, userID, now, wasCorrect, attemptType, timeSpent)
		if err != nil {
			// Log error but don't fail the operation
			fmt.Printf("Failed to track learning attempt stats: %v\n", err)
		}

		// Track puzzle mastery if applicable
		if wasMastered {
			err := r.userLearningDailyStatsRepo.IncrementPuzzlesMastered(ctx, userID, now)
			if err != nil {
				// Log error but don't fail the operation
				fmt.Printf("Failed to track puzzle mastery stats: %v\n", err)
			}
		}
	}

	return nil
}

// ExistsInQueue checks if a puzzle exists in the user's learning queue
func (r *LearningQueueRepository) ExistsInQueue(ctx context.Context, userID, lichessPuzzleID string) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	key := fmt.Sprintf("%s_%s", userID, lichessPuzzleID)
	_, exists := r.entries[key]
	return exists, nil
}

// RemoveFromQueue removes a puzzle from the queue (when mastered)
func (r *LearningQueueRepository) RemoveFromQueue(ctx context.Context, userID, lichessPuzzleID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	key := fmt.Sprintf("%s_%s", userID, lichessPuzzleID)
	delete(r.entries, key)
	return nil
}

// GetQueueStats retrieves learning queue statistics for a user
func (r *LearningQueueRepository) GetQueueStats(ctx context.Context, userID string) (*models.LearningQueueStats, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	stats := &models.LearningQueueStats{}
	now := time.Now()

	for _, entry := range r.entries {
		if entry.UserID != userID {
			continue
		}

		stats.TotalQueued++

		if entry.DueAt.Before(now) || entry.DueAt.Equal(now) {
			stats.DueToday++
		}

		switch entry.FailedAttemptType {
		case "regular":
			stats.RegularPuzzles++
		case "arrow_duel":
			stats.ArrowDuelPuzzles++
		}
	}

	// For fake implementation, return mock values for daily stats
	stats.DailyRetriesToday = 5
	stats.DailyRetriesThisWeek = 23
	stats.MasteryRate = 0.65

	return stats, nil
}

// GetByUserIDAndPuzzleID retrieves a specific learning queue entry
func (r *LearningQueueRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID, lichessPuzzleID string) (*models.LearningQueueEntry, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	key := fmt.Sprintf("%s_%s", userID, lichessPuzzleID)
	entry, exists := r.entries[key]
	if !exists {
		return nil, fmt.Errorf("learning queue entry not found")
	}

	// Return a copy to avoid race conditions
	entryCopy := *entry
	return &entryCopy, nil
}

package fake

import (
	"context"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// fakeSprintPuzzleAttempt is the GORM model used internally by the fake repository,
// storing array types as strings for SQLite compatibility.
type fakeSprintPuzzleAttempt struct {
	ID               string `gorm:"primaryKey"`
	SprintID         string `gorm:"index"`
	UserID           string `gorm:"index"`
	LichessPuzzleID  string `gorm:"index"`
	SequenceInSprint int
	UserMoves        string // Comma-separated string
	WasCorrect       bool
	TimeTakenMs      int
	AttemptedAt      time.Time
	AttemptType      string  // "regular" or "arrow_duel"
	CandidateMoves   string  // Comma-separated string for arrow-duel
	ChosenMove       *string // Move chosen by player for arrow-duel
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

// TableName specifies the table name for GORM
func (fakeSprintPuzzleAttempt) TableName() string {
	return "sprint_puzzle_attempts"
}

type SprintPuzzleAttemptRepository struct {
	db *gorm.DB
}

func NewSprintPuzzleAttemptRepository(db *gorm.DB) repository.ISprintPuzzleAttemptRepository {
	return &SprintPuzzleAttemptRepository{db: db}
}

// convertToFake converts a models.SprintPuzzleAttempt to fakeSprintPuzzleAttempt
func (r *SprintPuzzleAttemptRepository) convertToFake(attempt *models.SprintPuzzleAttempt) *fakeSprintPuzzleAttempt {
	var candidateMovesStr string
	if len(attempt.CandidateMoves) > 0 {
		candidateMovesStr = strings.Join(attempt.CandidateMoves, ",")
	}

	return &fakeSprintPuzzleAttempt{
		ID:               attempt.ID,
		SprintID:         attempt.SprintID,
		UserID:           attempt.UserID,
		LichessPuzzleID:  attempt.LichessPuzzleID,
		SequenceInSprint: attempt.SequenceInSprint,
		UserMoves:        strings.Join(attempt.UserMoves, ","),
		WasCorrect:       attempt.WasCorrect,
		TimeTakenMs:      attempt.TimeTakenMs,
		AttemptedAt:      attempt.AttemptedAt,
		AttemptType:      attempt.AttemptType,
		CandidateMoves:   candidateMovesStr,
		ChosenMove:       attempt.ChosenMove,
		CreatedAt:        attempt.CreatedAt,
		UpdatedAt:        attempt.UpdatedAt,
	}
}

// convertFromFake converts a fakeSprintPuzzleAttempt to models.SprintPuzzleAttempt
func (r *SprintPuzzleAttemptRepository) convertFromFake(fake *fakeSprintPuzzleAttempt) *models.SprintPuzzleAttempt {
	var userMoves []string
	if fake.UserMoves != "" {
		userMoves = strings.Split(fake.UserMoves, ",")
	}

	var candidateMoves []string
	if fake.CandidateMoves != "" {
		candidateMoves = strings.Split(fake.CandidateMoves, ",")
	}

	return &models.SprintPuzzleAttempt{
		ID:               fake.ID,
		SprintID:         fake.SprintID,
		UserID:           fake.UserID,
		LichessPuzzleID:  fake.LichessPuzzleID,
		SequenceInSprint: fake.SequenceInSprint,
		UserMoves:        userMoves,
		WasCorrect:       fake.WasCorrect,
		TimeTakenMs:      fake.TimeTakenMs,
		AttemptedAt:      fake.AttemptedAt,
		AttemptType:      fake.AttemptType,
		CandidateMoves:   candidateMoves,
		ChosenMove:       fake.ChosenMove,
		CreatedAt:        fake.CreatedAt,
		UpdatedAt:        fake.UpdatedAt,
	}
}

// Create creates a new sprint puzzle attempt
func (r *SprintPuzzleAttemptRepository) Create(ctx context.Context, attempt *models.SprintPuzzleAttempt) error {
	if attempt.ID == "" {
		attempt.ID = uuid.New().String()
	}
	now := time.Now()
	attempt.CreatedAt = now
	attempt.UpdatedAt = now

	fake := r.convertToFake(attempt)
	return r.db.WithContext(ctx).Create(fake).Error
}

// CreateBatch creates multiple sprint puzzle attempts in a single transaction
func (r *SprintPuzzleAttemptRepository) CreateBatch(ctx context.Context, attempts []models.SprintPuzzleAttempt) error {
	if len(attempts) == 0 {
		return nil
	}

	now := time.Now()
	fakeAttempts := make([]fakeSprintPuzzleAttempt, len(attempts))
	for i := range attempts {
		if attempts[i].ID == "" {
			attempts[i].ID = uuid.New().String()
		}
		attempts[i].CreatedAt = now
		attempts[i].UpdatedAt = now
		fakeAttempts[i] = *r.convertToFake(&attempts[i])
	}

	return r.db.WithContext(ctx).CreateInBatches(fakeAttempts, 100).Error
}

// GetBySprintAndUser retrieves all attempts for a specific sprint and user
func (r *SprintPuzzleAttemptRepository) GetBySprintAndUser(ctx context.Context, sprintID string, userID string) ([]models.SprintPuzzleAttempt, error) {
	var fakeAttempts []fakeSprintPuzzleAttempt
	err := r.db.WithContext(ctx).
		Where("sprint_id = ? AND user_id = ?", sprintID, userID).
		Order("sequence_in_sprint ASC").
		Find(&fakeAttempts).Error
	if err != nil {
		return nil, err
	}

	attempts := make([]models.SprintPuzzleAttempt, len(fakeAttempts))
	for i, fake := range fakeAttempts {
		attempts[i] = *r.convertFromFake(&fake)
	}
	return attempts, nil
}

// GetBySprintUserAndPuzzle retrieves a specific attempt for a sprint, user, and puzzle
func (r *SprintPuzzleAttemptRepository) GetBySprintUserAndPuzzle(ctx context.Context, sprintID string, userID string, puzzleID string) (*models.SprintPuzzleAttempt, error) {
	var fake fakeSprintPuzzleAttempt
	err := r.db.WithContext(ctx).
		Where("sprint_id = ? AND user_id = ? AND lichess_puzzle_id = ?", sprintID, userID, puzzleID).
		First(&fake).Error
	if err != nil {
		return nil, err
	}
	return r.convertFromFake(&fake), nil
}

// GetFailedAttempts retrieves all failed attempts for a specific sprint and user
func (r *SprintPuzzleAttemptRepository) GetFailedAttempts(ctx context.Context, sprintID string, userID string) ([]models.SprintPuzzleAttempt, error) {
	var fakeAttempts []fakeSprintPuzzleAttempt
	err := r.db.WithContext(ctx).
		Where("sprint_id = ? AND user_id = ? AND was_correct = ?", sprintID, userID, false).
		Order("sequence_in_sprint ASC").
		Find(&fakeAttempts).Error
	if err != nil {
		return nil, err
	}

	attempts := make([]models.SprintPuzzleAttempt, len(fakeAttempts))
	for i, fake := range fakeAttempts {
		attempts[i] = *r.convertFromFake(&fake)
	}
	return attempts, nil
}

// Update updates an existing sprint puzzle attempt
func (r *SprintPuzzleAttemptRepository) Update(ctx context.Context, attempt *models.SprintPuzzleAttempt) error {
	attempt.UpdatedAt = time.Now()
	fake := r.convertToFake(attempt)
	return r.db.WithContext(ctx).Save(fake).Error
}

package fake

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
)

// UserLearningDailyStatsRepository is a fake implementation of repository.IUserLearningDailyStatsRepository
type UserLearningDailyStatsRepository struct {
	mu    sync.RWMutex
	stats map[string]*models.UserLearningDailyStats // key: userID_date
}

// NewFakeUserLearningDailyStatsRepository creates a new fake user learning daily stats repository
func NewFakeUserLearningDailyStatsRepository(db *DB) repository.IUserLearningDailyStatsRepository {
	return &UserLearningDailyStatsRepository{
		stats: make(map[string]*models.UserLearningDailyStats),
	}
}

// CreateOrUpdate creates or updates daily learning stats
func (r *UserLearningDailyStatsRepository) CreateOrUpdate(ctx context.Context, stats *models.UserLearningDailyStats) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Ensure date is truncated to date only
	stats.Date = stats.Date.Truncate(24 * time.Hour)

	key := fmt.Sprintf("%s_%s", stats.UserID, stats.Date.Format("2006-01-02"))

	// Set ID if not set
	if stats.ID == "" {
		stats.ID = uuid.New().String()
	}

	// Set timestamps
	now := time.Now()
	if existing, exists := r.stats[key]; exists {
		stats.ID = existing.ID
		stats.CreatedAt = existing.CreatedAt
		stats.UpdatedAt = now
	} else {
		stats.CreatedAt = now
		stats.UpdatedAt = now
	}

	// Store a copy
	statsCopy := *stats
	r.stats[key] = &statsCopy

	return nil
}

// IncrementLearningAttempt increments learning attempt counters for a specific date
func (r *UserLearningDailyStatsRepository) IncrementLearningAttempt(ctx context.Context, userID string, date time.Time, wasCorrect bool, attemptType string, timeSpent int) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	dateOnly := date.Truncate(24 * time.Hour)
	key := fmt.Sprintf("%s_%s", userID, dateOnly.Format("2006-01-02"))

	// Get or create stats for the date
	stats, exists := r.stats[key]
	if !exists {
		stats = &models.UserLearningDailyStats{
			ID:     uuid.New().String(),
			UserID: userID,
			Date:   dateOnly,
		}
		now := time.Now()
		stats.CreatedAt = now
		stats.UpdatedAt = now
		r.stats[key] = stats
	}

	// Update counters
	stats.LearningAttempts++
	stats.TotalTimeSpent += timeSpent

	if wasCorrect {
		stats.LearningCorrect++
	}

	// Update attempt type specific counters
	switch attemptType {
	case "regular":
		stats.RegularRetries++
	case "arrow_duel":
		stats.ArrowDuelRetries++
	}

	stats.UpdatedAt = time.Now()

	return nil
}

// IncrementPuzzlesMastered increments the puzzles mastered counter for a specific date
func (r *UserLearningDailyStatsRepository) IncrementPuzzlesMastered(ctx context.Context, userID string, date time.Time) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	dateOnly := date.Truncate(24 * time.Hour)
	key := fmt.Sprintf("%s_%s", userID, dateOnly.Format("2006-01-02"))

	// Get or create stats for the date
	stats, exists := r.stats[key]
	if !exists {
		stats = &models.UserLearningDailyStats{
			ID:              uuid.New().String(),
			UserID:          userID,
			Date:            dateOnly,
			PuzzlesMastered: 1,
		}
		now := time.Now()
		stats.CreatedAt = now
		stats.UpdatedAt = now
		r.stats[key] = stats
	} else {
		// Update counter
		stats.PuzzlesMastered++
		stats.UpdatedAt = time.Now()
	}

	return nil
}

// GetByUserIDAndDateRange retrieves learning stats for a user within a date range
func (r *UserLearningDailyStatsRepository) GetByUserIDAndDateRange(ctx context.Context, userID string, startDate, endDate time.Time) ([]models.UserLearningDailyStats, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var result []models.UserLearningDailyStats

	// Truncate dates to date only
	startDate = startDate.Truncate(24 * time.Hour)
	endDate = endDate.Truncate(24 * time.Hour)

	for _, stats := range r.stats {
		if stats.UserID != userID {
			continue
		}

		statsDate := stats.Date.Truncate(24 * time.Hour)
		if (statsDate.Equal(startDate) || statsDate.After(startDate)) &&
			(statsDate.Equal(endDate) || statsDate.Before(endDate)) {
			// Return a copy to avoid race conditions
			statsCopy := *stats
			result = append(result, statsCopy)
		}
	}

	return result, nil
}

// GetByUserIDAndDate retrieves learning stats for a user on a specific date
func (r *UserLearningDailyStatsRepository) GetByUserIDAndDate(ctx context.Context, userID string, date time.Time) (*models.UserLearningDailyStats, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	dateOnly := date.Truncate(24 * time.Hour)
	key := fmt.Sprintf("%s_%s", userID, dateOnly.Format("2006-01-02"))

	stats, exists := r.stats[key]
	if !exists {
		return nil, fmt.Errorf("learning stats not found for user %s on date %s", userID, dateOnly.Format("2006-01-02"))
	}

	// Return a copy to avoid race conditions
	statsCopy := *stats
	return &statsCopy, nil
}

// DeleteByUserID deletes all learning stats for a user (for testing cleanup)
func (r *UserLearningDailyStatsRepository) DeleteByUserID(ctx context.Context, userID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	keysToDelete := make([]string, 0)
	for key, stats := range r.stats {
		if stats.UserID == userID {
			keysToDelete = append(keysToDelete, key)
		}
	}

	for _, key := range keysToDelete {
		delete(r.stats, key)
	}

	return nil
}

// GetWeeklyStats retrieves aggregated weekly learning stats for a user
func (r *UserLearningDailyStatsRepository) GetWeeklyStats(ctx context.Context, userID string, weekStart time.Time) ([]models.UserLearningDailyStats, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	weekStart = weekStart.Truncate(24 * time.Hour)
	weekEnd := weekStart.Add(7 * 24 * time.Hour)

	var weeklyStats []models.UserLearningDailyStats

	for _, stats := range r.stats {
		if stats.UserID != userID {
			continue
		}

		statsDate := stats.Date.Truncate(24 * time.Hour)
		if (statsDate.Equal(weekStart) || statsDate.After(weekStart)) &&
			statsDate.Before(weekEnd) {
			// Return a copy to avoid race conditions
			statsCopy := *stats
			weeklyStats = append(weeklyStats, statsCopy)
		}
	}

	return weeklyStats, nil
}

package testing

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDailyStatsSystemEndToEnd(t *testing.T) {
	// Use PostgreSQL for this test since it has the triggers
	provider := NewPostgresTestDBProvider()
	defer provider.Cleanup(t)

	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	eventRepo := provider.GetEventRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create a test game for the puzzles
	game := CreateTestGame(t, gameRepo, user.ID)

	// Test scenario: User solves puzzles over 3 consecutive days
	// Use fixed times at noon to avoid timezone issues
	day1Time := time.Date(2025, 5, 29, 12, 0, 0, 0, time.UTC)
	day2Time := time.Date(2025, 5, 30, 12, 0, 0, 0, time.UTC)
	day3Time := time.Date(2025, 5, 31, 12, 0, 0, 0, time.UTC)

	// Day 1: Create and solve 2 puzzles
	for i := 0; i < 2; i++ {
		puzzle := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		puzzleEventData := models.PuzzleEventData{
			PuzzleID:    puzzle.ID,
			PuzzleType:  models.PuzzleTypeUser,
			Solved:      true,
			TimeSpent:   30 + i*10,
			MovesPlayed: []string{"e4", "e5", "Nf3"},
		}
		puzzleEventDataJSON, err := json.Marshal(puzzleEventData)
		require.NoError(t, err)

		puzzleEvent := &models.Event{
			ID:        uuid.New().String(),
			UserID:    user.ID,
			EventType: models.EventTypePuzzle,
			EventData: json.RawMessage(puzzleEventDataJSON),
			EventTime: day1Time.Add(time.Duration(i) * time.Minute), // Use minutes to stay on same day
		}
		err = eventRepo.Create(context.Background(), puzzleEvent)
		require.NoError(t, err)
	}

	// Day 2: Create and solve 3 puzzles (1 failed)
	puzzleResults := []bool{true, false, true}
	for i, solved := range puzzleResults {
		puzzle := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		puzzleEventData := models.PuzzleEventData{
			PuzzleID:    puzzle.ID,
			PuzzleType:  models.PuzzleTypeUser,
			Solved:      solved,
			TimeSpent:   25 + i*5,
			MovesPlayed: []string{"e4", "e5", "Nf3"},
		}
		puzzleEventDataJSON, err := json.Marshal(puzzleEventData)
		require.NoError(t, err)

		puzzleEvent := &models.Event{
			ID:        uuid.New().String(),
			UserID:    user.ID,
			EventType: models.EventTypePuzzle,
			EventData: json.RawMessage(puzzleEventDataJSON),
			EventTime: day2Time.Add(time.Duration(i) * time.Minute), // Use minutes to stay on same day
		}
		err = eventRepo.Create(context.Background(), puzzleEvent)
		require.NoError(t, err)
	}

	// Day 3: Create and solve 1 puzzle
	day3Puzzle := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
	puzzleEventData := models.PuzzleEventData{
		PuzzleID:    day3Puzzle.ID,
		PuzzleType:  models.PuzzleTypeUser,
		Solved:      true,
		TimeSpent:   45,
		MovesPlayed: []string{"e4", "e5", "Nf3"},
	}
	puzzleEventDataJSON, err := json.Marshal(puzzleEventData)
	require.NoError(t, err)

	puzzleEvent := &models.Event{
		ID:        uuid.New().String(),
		UserID:    user.ID,
		EventType: models.EventTypePuzzle,
		EventData: json.RawMessage(puzzleEventDataJSON),
		EventTime: day3Time,
	}
	err = eventRepo.Create(context.Background(), puzzleEvent)
	require.NoError(t, err)

	// Wait for triggers to execute
	time.Sleep(300 * time.Millisecond)

	// Verify daily stats were created correctly
	var dailyStats []models.UserDailyStats
	err = provider.GetDB(t).Where("user_id = ?", user.ID).Order("date ASC").Find(&dailyStats).Error
	require.NoError(t, err)

	// Debug: Log what we actually got
	for i, ds := range dailyStats {
		t.Logf("Day %d (%s): success=%d, total=%d, streak=%d, duration=%d",
			i+1, ds.Date.Format("2006-01-02"), ds.PuzzleSuccess, ds.PuzzleTotal, ds.Streak, ds.PuzzleTotalDuration)
	}

	require.Len(t, dailyStats, 3, "Should have 3 daily stats records")

	// Day 1: 2 successful puzzles, streak = 1
	day1Stats := dailyStats[0]
	assert.Equal(t, 2, day1Stats.PuzzleSuccess, "Day 1 should have 2 successful puzzles")
	assert.Equal(t, 2, day1Stats.PuzzleTotal, "Day 1 should have 2 total puzzles")
	assert.Equal(t, 1, day1Stats.Streak, "Day 1 should have streak of 1")
	assert.Equal(t, 70, day1Stats.PuzzleTotalDuration, "Day 1 should have 70 seconds total (30+40)")

	// Day 2: 2 successful, 3 total, streak = 2
	day2Stats := dailyStats[1]
	assert.Equal(t, 2, day2Stats.PuzzleSuccess, "Day 2 should have 2 successful puzzles")
	assert.Equal(t, 3, day2Stats.PuzzleTotal, "Day 2 should have 3 total puzzles")
	assert.Equal(t, 2, day2Stats.Streak, "Day 2 should have streak of 2")
	assert.Equal(t, 90, day2Stats.PuzzleTotalDuration, "Day 2 should have 90 seconds total (25+30+35)")

	// Day 3: 1 successful, 1 total, streak = 3
	day3Stats := dailyStats[2]
	assert.Equal(t, 1, day3Stats.PuzzleSuccess, "Day 3 should have 1 successful puzzle")
	assert.Equal(t, 1, day3Stats.PuzzleTotal, "Day 3 should have 1 total puzzle")
	assert.Equal(t, 3, day3Stats.Streak, "Day 3 should have streak of 3")
	assert.Equal(t, 45, day3Stats.PuzzleTotalDuration, "Day 3 should have 45 seconds total")

	// Verify per-puzzle stats were created
	var puzzleStats []models.UserPuzzleStats
	err = provider.GetDB(t).Where("user_id = ?", user.ID).Find(&puzzleStats).Error
	require.NoError(t, err)
	assert.Len(t, puzzleStats, 6, "Should have 6 puzzle stats records (one per puzzle)")

	// Verify that all puzzles have correct last attempt data
	for _, ps := range puzzleStats {
		assert.Greater(t, ps.Attempts, 0, "Each puzzle should have at least 1 attempt")
		assert.NotZero(t, ps.LastAttemptTime, "Each puzzle should have last attempt time")
		// LastAttemptSuccess should match whether the puzzle was solved
	}

	t.Logf("✅ Daily stats system working correctly!")
	t.Logf("📊 Total stats: %d days, %d puzzles, %d successful", len(dailyStats), len(puzzleStats),
		day1Stats.PuzzleSuccess+day2Stats.PuzzleSuccess+day3Stats.PuzzleSuccess)
	t.Logf("🔥 Current streak: %d days", day3Stats.Streak)
}

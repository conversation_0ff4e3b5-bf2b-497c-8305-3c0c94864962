package testing

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDebugIncrementalStats(t *testing.T) {
	// Use PostgreSQL for this test since it has the triggers
	provider := NewPostgresTestDBProvider()
	defer provider.Cleanup(t)

	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	eventRepo := provider.GetEventRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)
	t.Logf("Created user with ID: %s", user.ID)

	// Create test game and puzzles
	game := CreateTestGame(t, gameRepo, user.ID)
	puzzle1 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
	puzzle2 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
	puzzle3 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

	// Create exactly 4 puzzle events as in the failing test
	puzzleEvents := []struct {
		puzzleID  string
		solved    bool
		timeSpent int
	}{
		{puzzle1.ID, true, 30},
		{puzzle2.ID, false, 60},
		{puzzle3.ID, true, 45},
		{puzzle1.ID, true, 25}, // Same puzzle again
	}

	// Use a fixed time for today to avoid issues with tests running near midnight
	today := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 12, 0, 0, 0, time.UTC)
	for i, pe := range puzzleEvents {
		puzzleEventData := models.PuzzleEventData{
			PuzzleID:    pe.puzzleID,
			PuzzleType:  models.PuzzleTypeUser,
			Solved:      pe.solved,
			TimeSpent:   pe.timeSpent,
			MovesPlayed: []string{"e4", "e5", "Nf3"},
		}
		puzzleEventDataJSON, err := json.Marshal(puzzleEventData)
		require.NoError(t, err)

		puzzleEvent := &models.Event{
			ID:        uuid.New().String(),
			UserID:    user.ID,
			EventType: models.EventTypePuzzle,
			EventData: json.RawMessage(puzzleEventDataJSON),
			EventTime: today.Add(time.Duration(i) * time.Minute), // Use minutes instead of hours
		}
		err = eventRepo.Create(context.Background(), puzzleEvent)
		require.NoError(t, err)
		t.Logf("Created puzzle event %d: puzzle=%s, solved=%v", i+1, pe.puzzleID, pe.solved)
	}

	// Wait for triggers to execute
	time.Sleep(500 * time.Millisecond)

	// Check database directly - user_stats table no longer exists, stats are in daily buckets
	t.Logf("Stats are now stored in daily buckets instead of user_stats table")

	// Check daily stats
	var dailyStats []models.UserDailyStats
	err := provider.GetDB(t).Where("user_id = ?", user.ID).Find(&dailyStats).Error
	require.NoError(t, err)

	for _, ds := range dailyStats {
		t.Logf("Daily stats for %s: success=%d, total=%d, streak=%d, duration=%d",
			ds.Date.Format("2006-01-02"), ds.PuzzleSuccess, ds.PuzzleTotal, ds.Streak, ds.PuzzleTotalDuration)
	}

	// Check that daily stats were created correctly
	// Expected: 3 successful (true, false, true, true), 4 total
	todayStr := today.Format("2006-01-02")
	var todayStats *models.UserDailyStats
	for _, ds := range dailyStats {
		if ds.Date.Format("2006-01-02") == todayStr {
			todayStats = &ds
			break
		}
	}

	require.NotNil(t, todayStats, "Should have today's daily stats")
	assert.Equal(t, 3, todayStats.PuzzleSuccess, "Should have 3 successful puzzles")
	assert.Equal(t, 4, todayStats.PuzzleTotal, "Should have 4 total puzzle attempts")
	assert.Greater(t, todayStats.Streak, 0, "Should have a positive streak")
	assert.Greater(t, todayStats.PuzzleTotalDuration, 0, "Should have total duration > 0")

	t.Logf("Today's stats: success=%d, total=%d, streak=%d, duration=%d",
		todayStats.PuzzleSuccess, todayStats.PuzzleTotal, todayStats.Streak, todayStats.PuzzleTotalDuration)
}

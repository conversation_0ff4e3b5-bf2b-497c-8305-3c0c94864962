// Package testing provides testing utilities for repository tests
package testing

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"os"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/lib/pq"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	testDB *gorm.DB
)

// RepositoryType defines the type of repository to use for tests
type RepositoryType string

const (
	// RealRepository uses a PostgreSQL database
	RealRepository RepositoryType = "real"
	// FakeRepository uses an in-memory SQLite database
	FakeRepository RepositoryType = "fake"
)

// TestDBProvider provides a database connection for tests
type TestDBProvider interface {
	// GetDB returns a gorm.DB instance ready for testing
	GetDB(t *testing.T) *gorm.DB
	// GetUserRepository returns a user repository
	GetUserRepository(t *testing.T) repository.IUserRepository
	// GetGameRepository returns a game repository
	GetGameRepository(t *testing.T) repository.IGameRepository
	// GetPuzzleRepository returns a puzzle repository
	GetPuzzleRepository(t *testing.T) repository.IPuzzleRepository
	// GetTaskRepository returns a task repository
	GetTaskRepository(t *testing.T) repository.ITaskRepository
	// GetPuzzleQueueRepository returns a puzzle queue repository
	GetPuzzleQueueRepository(t *testing.T) repository.IPuzzleQueueRepository
	// GetLearningQueueRepository returns a learning queue repository
	GetLearningQueueRepository(t *testing.T) repository.ILearningQueueRepository
	// GetUserLearningDailyStatsRepository returns a user learning daily stats repository
	GetUserLearningDailyStatsRepository(t *testing.T) repository.IUserLearningDailyStatsRepository
	// GetIdempotencyRepository returns an idempotency record repository
	GetIdempotencyRepository(t *testing.T) repository.IIdempotencyRepository
	// GetInvitationCodeRepository returns an invitation code repository
	GetInvitationCodeRepository(t *testing.T) repository.IInvitationCodeRepository
	// GetSessionTokenRepository returns a session token repository
	GetSessionTokenRepository(t *testing.T) repository.ISessionTokenRepository
	// GetEventRepository returns an event repository
	GetEventRepository(t *testing.T) repository.IEventRepository
	// Cleanup performs any necessary cleanup after tests
	Cleanup(t *testing.T)
}

// PostgresTestDBProvider uses a PostgreSQL database for testing
type PostgresTestDBProvider struct {
	db                         *gorm.DB
	userRepo                   repository.IUserRepository
	gameRepo                   repository.IGameRepository
	puzzleRepo                 repository.IPuzzleRepository
	taskRepo                   repository.ITaskRepository
	puzzleQueueRepo            repository.IPuzzleQueueRepository
	learningQueueRepo          repository.ILearningQueueRepository
	userLearningDailyStatsRepo repository.IUserLearningDailyStatsRepository
	idempotencyRepo            repository.IIdempotencyRepository
	invitationCodeRepo         repository.IInvitationCodeRepository
	sessionTokenRepo           repository.ISessionTokenRepository
	eventRepo                  repository.IEventRepository
}

// setupTestDB creates a test database connection
func setupTestDB(t *testing.T) *gorm.DB {
	if testDB != nil {
		return testDB
	}

	dsn := "host=localhost user=puzzler password=puzzler_secret dbname=chess_puzzler_dev port=5432 sslmode=disable"
	// Configure the logger of gorm
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Warn,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Auto-migrate all models
	err = db.AutoMigrate(
		&models.User{},
		&models.Game{},
		&models.Puzzle{},
		&models.Task{},
		&models.PuzzleQueueEntry{},
		&models.LearningQueueEntry{},
		&models.ChessProfile{},
		&models.IdempotencyRecord{},
		&models.InvitationCode{},
		&models.SessionToken{},
		&models.Event{},
		&models.UserPuzzleStats{},
		&models.UserLichessPuzzleStats{},
		&models.UserPuzzleArrowDuelStats{},
		&models.UserLichessPuzzleArrowDuelStats{},
		&models.UserDailyStats{},
		&models.UserLearningDailyStats{},
		&models.UserElo{},
		&models.Sprint{},
		&models.SprintPuzzle{},
		&models.SprintPuzzleAttempt{},
		&models.EloHistory{},
		&models.UserSprintDailyStats{},
	)
	if err != nil {
		t.Fatalf("Failed to migrate test database: %v", err)
	}

	testDB = db
	return db
}

// NewPostgresTestDBProvider creates a new PostgreSQL test DB provider
func NewPostgresTestDBProvider() *PostgresTestDBProvider {
	return &PostgresTestDBProvider{}
}

// GetDB returns a PostgreSQL database connection for testing
func (p *PostgresTestDBProvider) GetDB(t *testing.T) *gorm.DB {
	p.db = setupTestDB(t)
	return p.db
}

// GetUserRepository returns a PostgreSQL user repository
func (p *PostgresTestDBProvider) GetUserRepository(t *testing.T) repository.IUserRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.userRepo == nil {
		p.userRepo = repository.NewUserRepository(p.db)
	}
	return p.userRepo
}

// GetGameRepository returns a PostgreSQL game repository
func (p *PostgresTestDBProvider) GetGameRepository(t *testing.T) repository.IGameRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.gameRepo == nil {
		p.gameRepo = repository.NewGameRepository(p.db)
	}
	return p.gameRepo
}

// GetPuzzleRepository returns a PostgreSQL puzzle repository
func (p *PostgresTestDBProvider) GetPuzzleRepository(t *testing.T) repository.IPuzzleRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.puzzleRepo == nil {
		p.puzzleRepo = repository.NewPuzzleRepository(p.db)
	}
	return p.puzzleRepo
}

// GetTaskRepository returns a PostgreSQL task repository
func (p *PostgresTestDBProvider) GetTaskRepository(t *testing.T) repository.ITaskRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.taskRepo == nil {
		p.taskRepo = repository.NewTaskRepository(p.db)
	}
	return p.taskRepo
}

// GetPuzzleQueueRepository returns a PostgreSQL puzzle queue repository
func (p *PostgresTestDBProvider) GetPuzzleQueueRepository(t *testing.T) repository.IPuzzleQueueRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.puzzleQueueRepo == nil {
		p.puzzleQueueRepo = repository.NewPuzzleQueueRepository(p.db)
	}
	return p.puzzleQueueRepo
}

// GetLearningQueueRepository returns a PostgreSQL learning queue repository
func (p *PostgresTestDBProvider) GetLearningQueueRepository(t *testing.T) repository.ILearningQueueRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.learningQueueRepo == nil {
		userLearningDailyStatsRepo := p.GetUserLearningDailyStatsRepository(t)
		p.learningQueueRepo = repository.NewLearningQueueRepository(p.db, userLearningDailyStatsRepo)
	}
	return p.learningQueueRepo
}

// GetUserLearningDailyStatsRepository returns a PostgreSQL user learning daily stats repository
func (p *PostgresTestDBProvider) GetUserLearningDailyStatsRepository(t *testing.T) repository.IUserLearningDailyStatsRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.userLearningDailyStatsRepo == nil {
		p.userLearningDailyStatsRepo = repository.NewUserLearningDailyStatsRepository(p.db)
	}
	return p.userLearningDailyStatsRepo
}

// GetIdempotencyRepository returns a PostgreSQL idempotency record repository
func (p *PostgresTestDBProvider) GetIdempotencyRepository(t *testing.T) repository.IIdempotencyRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.idempotencyRepo == nil {
		p.idempotencyRepo = repository.NewIdempotencyRepository(p.db)
	}
	return p.idempotencyRepo
}

// GetInvitationCodeRepository returns a PostgreSQL invitation code repository
func (p *PostgresTestDBProvider) GetInvitationCodeRepository(t *testing.T) repository.IInvitationCodeRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.invitationCodeRepo == nil {
		p.invitationCodeRepo = repository.NewInvitationCodeRepository(p.db)
	}
	return p.invitationCodeRepo
}

// GetSessionTokenRepository returns a PostgreSQL session token repository
func (p *PostgresTestDBProvider) GetSessionTokenRepository(t *testing.T) repository.ISessionTokenRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.sessionTokenRepo == nil {
		p.sessionTokenRepo = repository.NewSessionTokenRepository(p.db)
	}
	return p.sessionTokenRepo
}

// GetEventRepository returns a PostgreSQL event repository
func (p *PostgresTestDBProvider) GetEventRepository(t *testing.T) repository.IEventRepository {
	if p.db == nil {
		p.GetDB(t)
	}
	if p.eventRepo == nil {
		p.eventRepo = repository.NewEventRepository(p.db)
	}
	return p.eventRepo
}

// Cleanup does nothing as PostgreSQL uses a shared test database
func (p *PostgresTestDBProvider) Cleanup(t *testing.T) {
	// Nothing to clean up as we reuse the test database
}

// SQLiteTestDBProvider uses an in-memory SQLite database for testing
type SQLiteTestDBProvider struct {
	db                         *fake.DB
	userRepo                   repository.IUserRepository
	gameRepo                   repository.IGameRepository
	puzzleRepo                 repository.IPuzzleRepository
	taskRepo                   repository.ITaskRepository
	puzzleQueueRepo            repository.IPuzzleQueueRepository
	learningQueueRepo          repository.ILearningQueueRepository
	userLearningDailyStatsRepo repository.IUserLearningDailyStatsRepository
	idempotencyRepo            repository.IIdempotencyRepository
	invitationCodeRepo         repository.IInvitationCodeRepository
	sessionTokenRepo           repository.ISessionTokenRepository
	eventRepo                  repository.IEventRepository
}

// NewSQLiteTestDBProvider creates a new SQLite test DB provider
func NewSQLiteTestDBProvider() *SQLiteTestDBProvider {
	return &SQLiteTestDBProvider{}
}

// GetDB returns an in-memory SQLite database connection for testing
func (p *SQLiteTestDBProvider) GetDB(t *testing.T) *gorm.DB {
	if p.db == nil {
		p.db = fake.NewDB(t)
	}
	return p.db.DB
}

// GetUserRepository returns a fake user repository using SQLite
func (p *SQLiteTestDBProvider) GetUserRepository(t *testing.T) repository.IUserRepository {
	if p.db == nil {
		p.db = fake.NewDB(t)
	}
	if p.userRepo == nil {
		p.userRepo = fake.NewUserRepository(p.db)
	}
	return p.userRepo
}

// GetGameRepository returns a fake game repository using SQLite
func (p *SQLiteTestDBProvider) GetGameRepository(t *testing.T) repository.IGameRepository {
	if p.db == nil {
		p.db = fake.NewDB(t)
	}
	if p.gameRepo == nil {
		p.gameRepo = fake.NewGameRepository(p.db)
	}
	return p.gameRepo
}

// GetPuzzleRepository returns a fake puzzle repository using SQLite
func (p *SQLiteTestDBProvider) GetPuzzleRepository(t *testing.T) repository.IPuzzleRepository {
	if p.db == nil {
		p.db = fake.NewDB(t)
	}
	if p.puzzleRepo == nil {
		p.puzzleRepo = fake.NewPuzzleRepository(p.db)
	}
	return p.puzzleRepo
}

// GetTaskRepository returns a fake task repository using SQLite
func (p *SQLiteTestDBProvider) GetTaskRepository(t *testing.T) repository.ITaskRepository {
	// Always use a fresh in-memory SQLite database for each test case to avoid state leakage
	if p.db != nil {
		// Close existing DB before creating a new one
		err := p.db.Close()
		require.NoError(t, err)
	}
	p.db = fake.NewDB(t)
	// Create a new fake task repository on the fresh DB
	p.taskRepo = fake.NewFakeTaskRepository(p.db)
	return p.taskRepo
}

// GetPuzzleQueueRepository returns a fake puzzle queue repository using SQLite
func (p *SQLiteTestDBProvider) GetPuzzleQueueRepository(t *testing.T) repository.IPuzzleQueueRepository {
	// Always use a fresh in-memory SQLite database for each test case to avoid state leakage
	if p.db != nil {
		// Close existing DB before creating a new one
		err := p.db.Close()
		require.NoError(t, err)
	}
	p.db = fake.NewDB(t)
	// Create a new fake puzzle queue repository on the fresh DB
	p.puzzleQueueRepo = fake.NewFakePuzzleQueueRepository(p.db)
	return p.puzzleQueueRepo
}

// GetLearningQueueRepository returns a fake learning queue repository using SQLite
func (p *SQLiteTestDBProvider) GetLearningQueueRepository(t *testing.T) repository.ILearningQueueRepository {
	// Always use a fresh in-memory SQLite database for each test case to avoid state leakage
	if p.db != nil {
		// Close existing DB before creating a new one
		err := p.db.Close()
		require.NoError(t, err)
	}
	p.db = fake.NewDB(t)
	// Create dependencies
	userLearningDailyStatsRepo := fake.NewFakeUserLearningDailyStatsRepository(p.db)
	// Create a new fake learning queue repository on the fresh DB
	p.learningQueueRepo = fake.NewFakeLearningQueueRepository(p.db, userLearningDailyStatsRepo)
	return p.learningQueueRepo
}

// GetUserLearningDailyStatsRepository returns a fake user learning daily stats repository using SQLite
func (p *SQLiteTestDBProvider) GetUserLearningDailyStatsRepository(t *testing.T) repository.IUserLearningDailyStatsRepository {
	if p.db == nil {
		p.db = fake.NewDB(t)
	}
	if p.userLearningDailyStatsRepo == nil {
		p.userLearningDailyStatsRepo = fake.NewFakeUserLearningDailyStatsRepository(p.db)
	}
	return p.userLearningDailyStatsRepo
}

// GetIdempotencyRepository returns a fake idempotency record repository using SQLite
func (p *SQLiteTestDBProvider) GetIdempotencyRepository(t *testing.T) repository.IIdempotencyRepository {
	// For Idempotency tests, we might want isolation similar to Tasks,
	// so let's ensure a fresh DB instance for each test case.
	if p.db != nil {
		err := p.db.Close()
		require.NoError(t, err)
	}
	p.db = fake.NewDB(t)
	// Create a new fake idempotency repository on the fresh DB
	p.idempotencyRepo = fake.NewFakeIdempotencyRepository(p.db)
	return p.idempotencyRepo
}

// GetInvitationCodeRepository returns a fake invitation code repository using SQLite
func (p *SQLiteTestDBProvider) GetInvitationCodeRepository(t *testing.T) repository.IInvitationCodeRepository {
	// For invitation code tests, we might want isolation similar to Tasks and Idempotency,
	// so let's ensure a fresh DB instance for each test case.
	if p.db != nil {
		err := p.db.Close()
		require.NoError(t, err)
	}
	p.db = fake.NewDB(t)
	// Create a new fake invitation code repository on the fresh DB
	p.invitationCodeRepo = fake.NewFakeInvitationCodeRepository(p.db.DB)
	return p.invitationCodeRepo
}

// GetSessionTokenRepository returns a fake session token repository using SQLite
func (p *SQLiteTestDBProvider) GetSessionTokenRepository(t *testing.T) repository.ISessionTokenRepository {
	if p.db == nil {
		p.db = fake.NewDB(t)
	}
	if p.sessionTokenRepo == nil {
		p.sessionTokenRepo = fake.NewFakeSessionTokenRepository(p.db.DB)
	}
	return p.sessionTokenRepo
}

// GetEventRepository returns a fake event repository using SQLite
func (p *SQLiteTestDBProvider) GetEventRepository(t *testing.T) repository.IEventRepository {
	if p.db == nil {
		p.db = fake.NewDB(t)
	}
	if p.eventRepo == nil {
		p.eventRepo = fake.NewEventRepository(p.db.DB)
	}
	return p.eventRepo
}

// Cleanup cleans up resources used by the SQLite database
func (p *SQLiteTestDBProvider) Cleanup(t *testing.T) {
	if p.db != nil {
		err := p.db.Close()
		require.NoError(t, err)
	}
}

// GetTestDBProvider returns a TestDBProvider based on the specified type
func GetTestDBProvider(repoType RepositoryType) TestDBProvider {
	switch repoType {
	case RealRepository:
		return NewPostgresTestDBProvider()
	case FakeRepository:
		return NewSQLiteTestDBProvider()
	default:
		// Default to SQLite for faster tests
		return NewSQLiteTestDBProvider()
	}
}

// RandomString generates a random string of specified length
func RandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}

// RandomEmail generates a random email address
func RandomEmail() string {
	return fmt.Sprintf("%<EMAIL>", RandomString(10))
}

// CreateTestUser creates a test user with random data and optional initial profiles
func CreateTestUser(t *testing.T, repo repository.IUserRepository, initialProfiles ...models.ChessProfile) *models.User {
	user := &models.User{
		Email:         RandomEmail(),
		PasswordHash:  RandomString(32),
		ChessProfiles: nil, // Profiles must be created separately now
	}
	err := repo.Create(context.Background(), user)
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}

	// Add profiles separately if provided
	ctx := context.Background()
	for _, profile := range initialProfiles {
		profileCopy := profile // Create a copy to avoid modifying the original
		err = repo.AddChessProfile(ctx, user.ID, &profileCopy)
		if err != nil {
			t.Fatalf("Failed to add chess profile: %v", err)
		}
	}

	// Return the updated user with profiles
	if len(initialProfiles) > 0 {
		updatedUser, err := repo.GetByID(ctx, user.ID)
		if err != nil {
			t.Fatalf("Failed to get user after adding profiles: %v", err)
		}
		return updatedUser
	}

	return user
}

// CreateTestGame creates a test game with random data
func CreateTestGame(t *testing.T, repo repository.IGameRepository, userID string) *models.Game {
	game := &models.Game{
		UserID:        userID,
		Platform:      models.ChessDotCom,
		ChessUsername: RandomString(10),
		UserColor:     models.White,
		GameTime:      time.Now(),
		CompressedPGN: []byte(RandomString(100)),
		TimeControl:   "5+0",
		Rated:         rand.Intn(2) == 1,
		WhitePlayer:   RandomString(100),
		BlackPlayer:   RandomString(100),
		Winner:        models.WinnerWhite,
		Result:        models.Mate,
	}
	err := repo.Create(context.Background(), game)
	if err != nil {
		t.Fatalf("Failed to create test game: %v", err)
	}
	return game
}

// CreateTestPuzzle creates a test puzzle with random data
func CreateTestPuzzle(t *testing.T, repo repository.IPuzzleRepository, gameID, userID string) *models.Puzzle {
	puzzle := &models.Puzzle{
		GameID:      gameID,
		UserID:      userID,
		GameMove:    rand.Intn(100),
		FEN:         RandomString(50),
		Moves:       []string{RandomString(5), RandomString(5)},
		PrevCP:      rand.Intn(1000) - 500,
		CP:          rand.Intn(1000) - 500,
		Theme:       models.OpponentMistakeCaught,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    rand.Intn(2) == 1,
		Tags:        pq.StringArray{RandomString(5), RandomString(5)},
	}
	err := repo.Create(context.Background(), puzzle)
	if err != nil {
		t.Fatalf("Failed to create test puzzle: %v", err)
	}
	return puzzle
}

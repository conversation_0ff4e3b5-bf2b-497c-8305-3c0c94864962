package repository

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/glebarez/sqlite"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// Auto-migrate the LichessPuzzle model
	err = db.AutoMigrate(&models.LichessPuzzle{})
	require.NoError(t, err)

	return db
}

func TestLichessPuzzleRepository_SaveBatch(t *testing.T) {
	db := setupTestDB(t)
	repo := NewLichessPuzzleRepository(db)
	ctx := context.Background()

	// Test data
	eval1 := 150.0
	eval2 := -75.0
	eval3 := 300.0
	evalAfter1 := 200.0
	evalAfter2 := -50.0

	puzzles := []models.LichessPuzzle{
		{
			ID:                         "00001",
			FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:                      pq.StringArray{"e2e4", "e7e5"},
			Rating:                     1500,
			RatingDeviation:            75,
			Popularity:                 90,
			NbPlays:                    1000,
			Themes:                     pq.StringArray{"opening"},
			GameURL:                    "https://lichess.org/test1",
			OpeningTags:                pq.StringArray{},
			BestMoveEval:               &eval1,
			BestMove:                   "e2e4",
			PositionEvalAfterFirstMove: &evalAfter1,
		},
		{
			ID:                         "00002",
			FEN:                        "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
			Moves:                      pq.StringArray{"g1f3", "b8c6"},
			Rating:                     1600,
			RatingDeviation:            80,
			Popularity:                 85,
			NbPlays:                    2000,
			Themes:                     pq.StringArray{"middlegame"},
			GameURL:                    "https://lichess.org/test2",
			OpeningTags:                pq.StringArray{},
			BestMoveEval:               &eval2,
			BestMove:                   "g1f3",
			PositionEvalAfterFirstMove: &evalAfter2,
		},
		{
			ID:              "00003",
			FEN:             "rnbqkbnr/pppp1ppp/8/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R b KQkq - 1 2",
			Moves:           pq.StringArray{"f8c5", "d2d3"},
			Rating:          1700,
			RatingDeviation: 85,
			Popularity:      80,
			NbPlays:         3000,
			Themes:          pq.StringArray{"endgame"},
			GameURL:         "https://lichess.org/test3",
			OpeningTags:     pq.StringArray{},
			BestMoveEval:    &eval3,
			BestMove:        "f8c5",
			// No PositionEvalAfterFirstMove for this one to test nil handling
		},
	}

	t.Run("SaveBatch with new puzzles", func(t *testing.T) {
		result, err := repo.SaveBatch(ctx, puzzles)
		require.NoError(t, err)
		assert.Equal(t, 3, result.Imported)
		assert.Equal(t, 0, result.Skipped)
		assert.Equal(t, "00003", result.LastID)
		assert.Empty(t, result.Errors)

		// Verify puzzles were saved
		for _, puzzle := range puzzles {
			saved, err := repo.GetByID(ctx, puzzle.ID)
			require.NoError(t, err)
			assert.Equal(t, puzzle.ID, saved.ID)
			assert.Equal(t, puzzle.FEN, saved.FEN)
			assert.Equal(t, puzzle.Rating, saved.Rating)

			// Verify new evaluation fields
			if puzzle.BestMoveEval != nil {
				assert.NotNil(t, saved.BestMoveEval)
				assert.Equal(t, *puzzle.BestMoveEval, *saved.BestMoveEval)
			} else {
				assert.Nil(t, saved.BestMoveEval)
			}
			assert.Equal(t, puzzle.BestMove, saved.BestMove)
			if puzzle.PositionEvalAfterFirstMove != nil {
				assert.NotNil(t, saved.PositionEvalAfterFirstMove)
				assert.Equal(t, *puzzle.PositionEvalAfterFirstMove, *saved.PositionEvalAfterFirstMove)
			} else {
				assert.Nil(t, saved.PositionEvalAfterFirstMove)
			}
		}
	})

	t.Run("SaveBatch with existing puzzles (idempotency)", func(t *testing.T) {
		// Modify one puzzle and try to save again
		puzzles[1].Rating = 1650 // Change rating

		result, err := repo.SaveBatch(ctx, puzzles)
		require.NoError(t, err)
		assert.Equal(t, 3, result.Imported)
		assert.Equal(t, 0, result.Skipped)
		assert.Equal(t, "00003", result.LastID)

		// Verify the updated puzzle
		saved, err := repo.GetByID(ctx, "00002")
		require.NoError(t, err)
		assert.Equal(t, 1650, saved.Rating)
	})

	t.Run("SaveBatch with empty slice", func(t *testing.T) {
		result, err := repo.SaveBatch(ctx, []models.LichessPuzzle{})
		require.NoError(t, err)
		assert.Equal(t, 0, result.Imported)
		assert.Equal(t, 0, result.Skipped)
		assert.Equal(t, "", result.LastID)
		assert.Empty(t, result.Errors)
	})
}

func TestLichessPuzzleRepository_Save(t *testing.T) {
	db := setupTestDB(t)
	repo := NewLichessPuzzleRepository(db)
	ctx := context.Background()

	bestMoveEval := 125.0
	positionEvalAfterFirstMove := 175.0

	puzzle := &models.LichessPuzzle{
		ID:                         "test001",
		FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:                      pq.StringArray{"e2e4", "e7e5"},
		Rating:                     1500,
		RatingDeviation:            75,
		Popularity:                 90,
		NbPlays:                    1000,
		Themes:                     pq.StringArray{"opening"},
		GameURL:                    "https://lichess.org/test",
		OpeningTags:                pq.StringArray{},
		BestMoveEval:               &bestMoveEval,
		BestMove:                   "e2e4",
		PositionEvalAfterFirstMove: &positionEvalAfterFirstMove,
	}

	t.Run("Save new puzzle", func(t *testing.T) {
		err := repo.Save(ctx, puzzle)
		require.NoError(t, err)

		// Verify timestamps were set
		assert.False(t, puzzle.CreatedAt.IsZero())
		assert.False(t, puzzle.UpdatedAt.IsZero())

		// Verify puzzle was saved
		saved, err := repo.GetByID(ctx, puzzle.ID)
		require.NoError(t, err)
		assert.Equal(t, puzzle.ID, saved.ID)
		assert.Equal(t, puzzle.FEN, saved.FEN)

		// Verify new evaluation fields
		assert.NotNil(t, saved.BestMoveEval)
		assert.Equal(t, 125.0, *saved.BestMoveEval)
		assert.Equal(t, "e2e4", saved.BestMove)
		assert.NotNil(t, saved.PositionEvalAfterFirstMove)
		assert.Equal(t, 175.0, *saved.PositionEvalAfterFirstMove)
	})

	t.Run("Save existing puzzle (update)", func(t *testing.T) {
		originalUpdatedAt := puzzle.UpdatedAt
		time.Sleep(time.Millisecond) // Ensure different timestamp

		// Change rating and evaluation fields
		puzzle.Rating = 1600
		newBestMoveEval := 200.0
		puzzle.BestMoveEval = &newBestMoveEval
		puzzle.BestMove = "d2d4"

		err := repo.Save(ctx, puzzle)
		require.NoError(t, err)

		// Verify UpdatedAt was changed
		assert.True(t, puzzle.UpdatedAt.After(originalUpdatedAt))

		// Verify puzzle was updated
		saved, err := repo.GetByID(ctx, puzzle.ID)
		require.NoError(t, err)
		assert.Equal(t, 1600, saved.Rating)
		assert.NotNil(t, saved.BestMoveEval)
		assert.Equal(t, 200.0, *saved.BestMoveEval)
		assert.Equal(t, "d2d4", saved.BestMove)
	})
}

func TestLichessPuzzleRepository_GetByIDs(t *testing.T) {
	db := setupTestDB(t)
	repo := NewLichessPuzzleRepository(db)
	ctx := context.Background()

	// Create test puzzles
	puzzles := []models.LichessPuzzle{
		{
			ID:              "batch001",
			FEN:             "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:           pq.StringArray{"e2e4", "e7e5"},
			Rating:          1500,
			RatingDeviation: 75,
			Popularity:      90,
			NbPlays:         1000,
			Themes:          pq.StringArray{"opening"},
			GameURL:         "https://lichess.org/batch1",
			OpeningTags:     pq.StringArray{},
		},
		{
			ID:              "batch002",
			FEN:             "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
			Moves:           pq.StringArray{"g1f3", "b8c6"},
			Rating:          1600,
			RatingDeviation: 80,
			Popularity:      85,
			NbPlays:         2000,
			Themes:          pq.StringArray{"middlegame"},
			GameURL:         "https://lichess.org/batch2",
			OpeningTags:     pq.StringArray{},
		},
	}

	// Save test puzzles
	for _, puzzle := range puzzles {
		err := repo.Save(ctx, &puzzle)
		require.NoError(t, err)
	}

	t.Run("GetByIDs with existing puzzles", func(t *testing.T) {
		ids := []string{"batch001", "batch002"}
		result, err := repo.GetByIDs(ctx, ids)
		require.NoError(t, err)
		assert.Len(t, result, 2)

		// Verify both puzzles are returned
		assert.Contains(t, result, "batch001")
		assert.Contains(t, result, "batch002")
		assert.Equal(t, "batch001", result["batch001"].ID)
		assert.Equal(t, "batch002", result["batch002"].ID)
		assert.Equal(t, 1500, result["batch001"].Rating)
		assert.Equal(t, 1600, result["batch002"].Rating)
	})

	t.Run("GetByIDs with some non-existent puzzles", func(t *testing.T) {
		ids := []string{"batch001", "nonexistent", "batch002"}
		result, err := repo.GetByIDs(ctx, ids)
		require.NoError(t, err)
		assert.Len(t, result, 2) // Only existing puzzles returned

		assert.Contains(t, result, "batch001")
		assert.Contains(t, result, "batch002")
		assert.NotContains(t, result, "nonexistent")
	})

	t.Run("GetByIDs with empty slice", func(t *testing.T) {
		result, err := repo.GetByIDs(ctx, []string{})
		require.NoError(t, err)
		assert.Len(t, result, 0)
	})

	t.Run("GetByIDs with all non-existent puzzles", func(t *testing.T) {
		ids := []string{"nonexistent1", "nonexistent2"}
		result, err := repo.GetByIDs(ctx, ids)
		require.NoError(t, err)
		assert.Len(t, result, 0)
	})
}

func TestLichessPuzzleRepository_UpdateEvaluationsBatch(t *testing.T) {
	db := setupTestDB(t)
	repo := NewLichessPuzzleRepository(db)
	ctx := context.Background()

	// First, create some test puzzles without evaluation data
	initialPuzzles := []models.LichessPuzzle{
		{
			ID:              "eval001",
			FEN:             "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:           pq.StringArray{"e2e4", "e7e5"},
			Rating:          1500,
			RatingDeviation: 75,
			Popularity:      90,
			NbPlays:         1000,
			Themes:          pq.StringArray{"opening"},
			GameURL:         "https://lichess.org/eval1",
			OpeningTags:     pq.StringArray{},
		},
		{
			ID:              "eval002",
			FEN:             "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
			Moves:           pq.StringArray{"g1f3", "b8c6"},
			Rating:          1600,
			RatingDeviation: 80,
			Popularity:      85,
			NbPlays:         2000,
			Themes:          pq.StringArray{"middlegame"},
			GameURL:         "https://lichess.org/eval2",
			OpeningTags:     pq.StringArray{},
		},
	}

	// Save initial puzzles
	for _, puzzle := range initialPuzzles {
		err := repo.Save(ctx, &puzzle)
		require.NoError(t, err)
	}

	t.Run("UpdateEvaluationsBatch with valid data", func(t *testing.T) {
		eval1 := 125.0
		eval2 := -75.0
		evalAfter1 := 200.0
		evalAfter2 := -50.0

		// Create puzzles with evaluation data
		puzzlesWithEvals := []models.LichessPuzzle{
			{
				ID:                         "eval001",
				FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:                      pq.StringArray{"e2e4", "e7e5"},
				Rating:                     1500,
				RatingDeviation:            75,
				Popularity:                 90,
				NbPlays:                    1000,
				Themes:                     pq.StringArray{"opening"},
				GameURL:                    "https://lichess.org/eval1",
				OpeningTags:                pq.StringArray{},
				BestMoveEval:               &eval1,
				BestMove:                   "e2e4",
				PositionEvalAfterFirstMove: &evalAfter1,
			},
			{
				ID:                         "eval002",
				FEN:                        "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
				Moves:                      pq.StringArray{"g1f3", "b8c6"},
				Rating:                     1600,
				RatingDeviation:            80,
				Popularity:                 85,
				NbPlays:                    2000,
				Themes:                     pq.StringArray{"middlegame"},
				GameURL:                    "https://lichess.org/eval2",
				OpeningTags:                pq.StringArray{},
				BestMoveEval:               &eval2,
				BestMove:                   "g1f3",
				PositionEvalAfterFirstMove: &evalAfter2,
			},
		}

		result, err := repo.UpdateEvaluationsBatch(ctx, puzzlesWithEvals)
		require.NoError(t, err)
		assert.Equal(t, 2, result.Imported)
		assert.Equal(t, 0, result.Skipped)
		assert.Equal(t, "eval002", result.LastID)
		assert.Empty(t, result.Errors)

		// Verify evaluations were updated
		saved1, err := repo.GetByID(ctx, "eval001")
		require.NoError(t, err)
		assert.NotNil(t, saved1.BestMoveEval)
		assert.Equal(t, 125.0, *saved1.BestMoveEval)
		assert.Equal(t, "e2e4", saved1.BestMove)
		assert.NotNil(t, saved1.PositionEvalAfterFirstMove)
		assert.Equal(t, 200.0, *saved1.PositionEvalAfterFirstMove)

		saved2, err := repo.GetByID(ctx, "eval002")
		require.NoError(t, err)
		assert.NotNil(t, saved2.BestMoveEval)
		assert.Equal(t, -75.0, *saved2.BestMoveEval)
		assert.Equal(t, "g1f3", saved2.BestMove)
		assert.NotNil(t, saved2.PositionEvalAfterFirstMove)
		assert.Equal(t, -50.0, *saved2.PositionEvalAfterFirstMove)
	})

	t.Run("UpdateEvaluationsBatch with changed non-evaluation data", func(t *testing.T) {
		eval1 := 300.0

		// Try to update with changed rating (should fail)
		puzzleWithChangedData := []models.LichessPuzzle{
			{
				ID:                         "eval001",
				FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:                      pq.StringArray{"e2e4", "e7e5"},
				Rating:                     1550, // Changed rating
				RatingDeviation:            75,
				Popularity:                 90,
				NbPlays:                    1000,
				Themes:                     pq.StringArray{"opening"},
				GameURL:                    "https://lichess.org/eval1",
				OpeningTags:                pq.StringArray{},
				BestMoveEval:               &eval1,
				BestMove:                   "e2e4",
				PositionEvalAfterFirstMove: nil,
			},
		}

		result, err := repo.UpdateEvaluationsBatch(ctx, puzzleWithChangedData)
		require.NoError(t, err)
		assert.Equal(t, 0, result.Imported)
		assert.Equal(t, 1, result.Skipped)
		assert.Len(t, result.Errors, 1)
		assert.Contains(t, result.Errors[0].Error(), "has changed non-evaluation data")
	})

	t.Run("UpdateEvaluationsBatch with non-existent puzzle", func(t *testing.T) {
		eval1 := 100.0

		nonExistentPuzzle := []models.LichessPuzzle{
			{
				ID:           "nonexistent",
				BestMoveEval: &eval1,
				BestMove:     "e2e4",
			},
		}

		result, err := repo.UpdateEvaluationsBatch(ctx, nonExistentPuzzle)
		require.NoError(t, err)
		assert.Equal(t, 0, result.Imported)
		assert.Equal(t, 1, result.Skipped)
		assert.Len(t, result.Errors, 1)
		assert.Contains(t, result.Errors[0].Error(), "not found")
	})

	t.Run("UpdateEvaluationsBatch with repeated updates", func(t *testing.T) {
		// First update
		eval1 := 100.0
		evalAfter1 := 150.0

		firstUpdate := []models.LichessPuzzle{
			{
				ID:                         "eval001",
				FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:                      pq.StringArray{"e2e4", "e7e5"},
				Rating:                     1500,
				RatingDeviation:            75,
				Popularity:                 90,
				NbPlays:                    1000,
				Themes:                     pq.StringArray{"opening"},
				GameURL:                    "https://lichess.org/eval1",
				OpeningTags:                pq.StringArray{},
				BestMoveEval:               &eval1,
				BestMove:                   "e2e4",
				PositionEvalAfterFirstMove: &evalAfter1,
			},
		}

		result, err := repo.UpdateEvaluationsBatch(ctx, firstUpdate)
		require.NoError(t, err)
		assert.Equal(t, 1, result.Imported)
		assert.Equal(t, 0, result.Skipped)

		// Verify first update
		saved, err := repo.GetByID(ctx, "eval001")
		require.NoError(t, err)
		assert.NotNil(t, saved.BestMoveEval)
		assert.Equal(t, 100.0, *saved.BestMoveEval)
		assert.Equal(t, "e2e4", saved.BestMove)
		assert.NotNil(t, saved.PositionEvalAfterFirstMove)
		assert.Equal(t, 150.0, *saved.PositionEvalAfterFirstMove)

		// Second update with different evaluation values
		eval2 := 250.0
		evalAfter2 := 300.0

		secondUpdate := []models.LichessPuzzle{
			{
				ID:                         "eval001",
				FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:                      pq.StringArray{"e2e4", "e7e5"},
				Rating:                     1500,
				RatingDeviation:            75,
				Popularity:                 90,
				NbPlays:                    1000,
				Themes:                     pq.StringArray{"opening"},
				GameURL:                    "https://lichess.org/eval1",
				OpeningTags:                pq.StringArray{},
				BestMoveEval:               &eval2,
				BestMove:                   "d2d4", // Changed best move
				PositionEvalAfterFirstMove: &evalAfter2,
			},
		}

		result, err = repo.UpdateEvaluationsBatch(ctx, secondUpdate)
		require.NoError(t, err)
		assert.Equal(t, 1, result.Imported)
		assert.Equal(t, 0, result.Skipped)

		// Verify second update overwrote the first
		saved, err = repo.GetByID(ctx, "eval001")
		require.NoError(t, err)
		assert.NotNil(t, saved.BestMoveEval)
		assert.Equal(t, 250.0, *saved.BestMoveEval)
		assert.Equal(t, "d2d4", saved.BestMove) // Changed best move
		assert.NotNil(t, saved.PositionEvalAfterFirstMove)
		assert.Equal(t, 300.0, *saved.PositionEvalAfterFirstMove)
	})

	t.Run("UpdateEvaluationsBatch performance test", func(t *testing.T) {
		// Create a larger batch to test performance
		const batchSize = 50
		var largeBatch []models.LichessPuzzle

		// First, create and save the puzzles
		for i := 0; i < batchSize; i++ {
			puzzleID := fmt.Sprintf("perf%03d", i)
			puzzle := models.LichessPuzzle{
				ID:              puzzleID,
				FEN:             "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:           pq.StringArray{"e2e4", "e7e5"},
				Rating:          1500 + i,
				RatingDeviation: 75,
				Popularity:      90,
				NbPlays:         1000,
				Themes:          pq.StringArray{"opening"},
				GameURL:         fmt.Sprintf("https://lichess.org/perf%d", i),
				OpeningTags:     pq.StringArray{},
			}
			err := repo.Save(ctx, &puzzle)
			require.NoError(t, err)
		}

		// Now prepare the batch update with evaluation data
		for i := 0; i < batchSize; i++ {
			puzzleID := fmt.Sprintf("perf%03d", i)
			eval := float64(100 + i)
			evalAfter := float64(200 + i)

			puzzle := models.LichessPuzzle{
				ID:                         puzzleID,
				FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:                      pq.StringArray{"e2e4", "e7e5"},
				Rating:                     1500 + i,
				RatingDeviation:            75,
				Popularity:                 90,
				NbPlays:                    1000,
				Themes:                     pq.StringArray{"opening"},
				GameURL:                    fmt.Sprintf("https://lichess.org/perf%d", i),
				OpeningTags:                pq.StringArray{},
				BestMoveEval:               &eval,
				BestMove:                   "e2e4",
				PositionEvalAfterFirstMove: &evalAfter,
			}
			largeBatch = append(largeBatch, puzzle)
		}

		// Measure the batch update performance
		start := time.Now()
		result, err := repo.UpdateEvaluationsBatch(ctx, largeBatch)
		duration := time.Since(start)

		require.NoError(t, err)
		assert.Equal(t, batchSize, result.Imported)
		assert.Equal(t, 0, result.Skipped)
		assert.Empty(t, result.Errors)

		// Performance should be reasonable (less than 1 second for 50 updates)
		assert.Less(t, duration, time.Second, "Batch update should complete in less than 1 second")

		t.Logf("Updated %d puzzles in %v (%.2f puzzles/second)",
			batchSize, duration, float64(batchSize)/duration.Seconds())

		// Verify a few random puzzles were updated correctly
		saved, err := repo.GetByID(ctx, "perf025")
		require.NoError(t, err)
		assert.NotNil(t, saved.BestMoveEval)
		assert.Equal(t, 125.0, *saved.BestMoveEval)
		assert.Equal(t, "e2e4", saved.BestMove)
		assert.NotNil(t, saved.PositionEvalAfterFirstMove)
		assert.Equal(t, 225.0, *saved.PositionEvalAfterFirstMove)
	})
}

func TestLichessPuzzleRepository_ArrowDuelFiltering(t *testing.T) {
	db := setupTestDB(t)
	repo := NewLichessPuzzleRepository(db)
	ctx := context.Background()

	// Create test puzzles with various evaluation scenarios
	puzzles := []models.LichessPuzzle{
		{
			ID:                         "arrow_valid_black",
			FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR b KQkq - 0 1", // Black to move
			Moves:                      pq.StringArray{"e7e5"},
			Rating:                     1500,
			RatingDeviation:            75,
			Popularity:                 90,
			NbPlays:                    1000,
			Themes:                     pq.StringArray{"tactics"},
			GameURL:                    "https://lichess.org/test1",
			OpeningTags:                pq.StringArray{},
			BestMoveEval:               floatPtr(50.0), // <= 60 (good for black)
			BestMove:                   "e7e5",
			PositionEvalAfterFirstMove: floatPtr(300.0), // > 0 (black's turn), diff = 250 > 200
		},
		{
			ID:                         "arrow_valid_white",
			FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", // White to move
			Moves:                      pq.StringArray{"e2e4"},
			Rating:                     1500,
			RatingDeviation:            75,
			Popularity:                 90,
			NbPlays:                    1000,
			Themes:                     pq.StringArray{"tactics"},
			GameURL:                    "https://lichess.org/test2",
			OpeningTags:                pq.StringArray{},
			BestMoveEval:               floatPtr(-50.0), // >= -60 (good for white)
			BestMove:                   "e2e4",
			PositionEvalAfterFirstMove: floatPtr(-300.0), // < 0 (white's turn), diff = 250 > 200
		},
		{
			ID:                         "arrow_invalid_eval_too_bad",
			FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR b KQkq - 0 1", // Black to move
			Moves:                      pq.StringArray{"e7e5"},
			Rating:                     1500,
			RatingDeviation:            75,
			Popularity:                 90,
			NbPlays:                    1000,
			Themes:                     pq.StringArray{"tactics"},
			GameURL:                    "https://lichess.org/test3",
			OpeningTags:                pq.StringArray{},
			BestMoveEval:               floatPtr(80.0), // > 60 (too bad for black)
			BestMove:                   "e7e5",
			PositionEvalAfterFirstMove: floatPtr(300.0), // > 0 (black's turn)
		},
		{
			ID:                         "arrow_invalid_diff_too_small",
			FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", // White to move
			Moves:                      pq.StringArray{"e2e4"},
			Rating:                     1500,
			RatingDeviation:            75,
			Popularity:                 90,
			NbPlays:                    1000,
			Themes:                     pq.StringArray{"tactics"},
			GameURL:                    "https://lichess.org/test4",
			OpeningTags:                pq.StringArray{},
			BestMoveEval:               floatPtr(-50.0), // >= -60 (good for white)
			BestMove:                   "e2e4",
			PositionEvalAfterFirstMove: floatPtr(-150.0), // < 0 (white's turn), diff = 100 < 200
		},
		{
			ID:                         "arrow_invalid_null_eval",
			FEN:                        "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:                      pq.StringArray{"e2e4"},
			Rating:                     1500,
			RatingDeviation:            75,
			Popularity:                 90,
			NbPlays:                    1000,
			Themes:                     pq.StringArray{"tactics"},
			GameURL:                    "https://lichess.org/test5",
			OpeningTags:                pq.StringArray{},
			BestMoveEval:               nil, // Null evaluation
			BestMove:                   "",
			PositionEvalAfterFirstMove: floatPtr(-300.0),
		},
		{
			ID:              "regular_puzzle",
			FEN:             "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:           pq.StringArray{"e2e4"},
			Rating:          1500,
			RatingDeviation: 75,
			Popularity:      90,
			NbPlays:         1000,
			Themes:          pq.StringArray{"tactics"},
			GameURL:         "https://lichess.org/test6",
			OpeningTags:     pq.StringArray{},
			// No evaluation fields for regular puzzle
		},
	}

	// Save all test puzzles
	for _, puzzle := range puzzles {
		err := repo.Save(ctx, &puzzle)
		require.NoError(t, err)
	}

	t.Run("ArrowDuelFiltering_ValidPuzzles", func(t *testing.T) {
		filter := common.LichessPuzzleFilter{
			MinRating:     1400,
			MaxRating:     1600,
			ArrowDuelOnly: true,
		}

		puzzles, err := repo.GetRandomPuzzles(ctx, filter, 10)
		require.NoError(t, err)

		// Should only return the 2 valid arrow-duel puzzles
		assert.Len(t, puzzles, 2)

		// Verify the returned puzzles are the valid ones
		puzzleIDs := make([]string, len(puzzles))
		for i, p := range puzzles {
			puzzleIDs[i] = p.ID
		}
		assert.Contains(t, puzzleIDs, "arrow_valid_black")
		assert.Contains(t, puzzleIDs, "arrow_valid_white")
	})

	t.Run("ArrowDuelFiltering_Disabled", func(t *testing.T) {
		filter := common.LichessPuzzleFilter{
			MinRating:     1400,
			MaxRating:     1600,
			ArrowDuelOnly: false, // Regular filtering
		}

		puzzles, err := repo.GetRandomPuzzles(ctx, filter, 10)
		require.NoError(t, err)

		// Should return all puzzles (including those without evaluations)
		assert.Len(t, puzzles, 6)
	})

	// TODO: Add test for ArrowDuelFiltering_WithThemes
	// This requires PostgreSQL-compatible theme filtering (themes && ?) which doesn't work in SQLite tests
}

// Helper function to create float64 pointers
func floatPtr(f float64) *float64 {
	return &f
}

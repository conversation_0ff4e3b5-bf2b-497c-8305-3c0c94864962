package common

import (
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
)

// GameFilter holds criteria for filtering games. Zero values are ignored.
type GameFilter struct {
	ChessProfileID *string               // Optional: Keep for context, but filtering uses Platform/Username
	Platform       *models.ChessPlatform // Optional: Filter by platform
	ChessUsername  *string               // Optional: Filter by username on that platform
	StartTime      *time.Time            // Optional: Filter games played after this time
	EndTime        *time.Time            // Optional: Filter games played before this time
	UserColor      *models.Color         // Optional: Filter by user's color
	TimeControl    *string               // Optional: Filter by time control
	Rated          *bool                 // Optional: Filter by rated status
	Result         *models.GameResult    // Optional: Filter by game result
	OmitPGN        bool                  // Optional: If true, don't load the CompressedPGN field
}

// PuzzleFilter holds criteria for filtering puzzles. Zero values/empty slices are ignored.
type PuzzleFilter struct {
	Tags          []string             // Optional: Filter by tags (match any with OR relation)
	GameStartTime *time.Time           // Optional: Filter puzzles from games played after this time
	GameEndTime   *time.Time           // Optional: Filter puzzles from games played before this time
	Themes        []models.PuzzleTheme // Optional: Filter by puzzle themes (match any with OR relation)
	UserColor     *models.Color        // Optional: Filter by user's color
	PuzzleColor   *models.Color        // Optional: Filter by puzzle color
	GameMoveMin   *int                 // Optional: Filter by minimum game move
	GameMoveMax   *int                 // Optional: Filter by maximum game move
	PrevCpMin     *int                 // Optional: Filter by minimum previous centipawn value
	PrevCpMax     *int                 // Optional: Filter by maximum previous centipawn value
	CpChangeMin   *int                 // Optional: Filter by minimum centipawn change
	CpChangeMax   *int                 // Optional: Filter by maximum centipawn change
	TimeControl   *string              // Optional: Filter by game time control
	Rated         *bool                // Optional: Filter by game rated status
}

// LichessPuzzleFilter holds criteria for filtering random lichess puzzles
type LichessPuzzleFilter struct {
	MinRating     int      // Minimum rating (required)
	MaxRating     int      // Maximum rating (required)
	Themes        []string // Optional: Filter by themes (match any with OR relation)
	ExcludeIDs    []string // Optional: Exclude specific puzzle IDs
	ArrowDuelOnly bool     // Optional: Filter for arrow-duel compatible puzzles only
}

// UserPuzzleFilter holds criteria for filtering random user puzzles
type UserPuzzleFilter struct {
	MinMoveLength int        // Minimum move length (puzzle length in moves, always even numbers, required)
	MaxMoveLength int        // Maximum move length (puzzle length in moves, always even numbers, required)
	Tags          []string   // Optional: Filter by tags (match any with OR relation)
	Themes        []string   // Optional: Filter by themes (match any with OR relation)
	GameTimeStart *time.Time // Optional: Filter by game time start
	GameTimeEnd   *time.Time // Optional: Filter by game time end
	DislikedIDs   []string   // Optional: Exclude disliked puzzle IDs
}

// TimeGroupingUnit represents the unit for time grouping (day, week, month)
type TimeGroupingUnit string

const (
	// TimeGroupingDay represents daily grouping
	TimeGroupingDay TimeGroupingUnit = "DAY"
	// TimeGroupingWeek represents weekly grouping
	TimeGroupingWeek TimeGroupingUnit = "WEEK"
	// TimeGroupingMonth represents monthly grouping
	TimeGroupingMonth TimeGroupingUnit = "MONTH"
)

// TimeGrouping represents parameters for grouping by time periods
type TimeGrouping struct {
	// Unit is the time unit to group by (DAY, WEEK, MONTH)
	Unit TimeGroupingUnit
	// Length is the number of units in each group
	Length int
}

// TimeGroup represents a time period with start and end times
// The time span is exclusive start, inclusive end: startTime < time <= endTime
type TimeGroup struct {
	StartTime time.Time
	EndTime   time.Time
}

// IsValid returns true if the TimeGrouping has valid values
func (tg *TimeGrouping) IsValid() bool {
	if tg == nil {
		return false
	}

	// Check if Unit is valid
	validUnit := tg.Unit == TimeGroupingDay || tg.Unit == TimeGroupingWeek || tg.Unit == TimeGroupingMonth

	// Check if Length is valid
	validLength := tg.Length > 0

	return validUnit && validLength
}

// addMonthsWithDayAdjustment adds months to a date while handling day overflow
// If the target month doesn't have the originalDay, it adjusts to the last day of that month
func addMonthsWithDayAdjustment(originalDay int, t time.Time, months int) time.Time {
	// Calculate the target year and month by adding months to the first day of the current month
	// This avoids day overflow affecting the month calculation
	firstDayOfCurrentMonth := time.Date(t.Year(), t.Month(), 1, t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), t.Location())
	firstDayOfTargetMonth := firstDayOfCurrentMonth.AddDate(0, months, 0)
	targetYear := firstDayOfTargetMonth.Year()
	targetMonth := firstDayOfTargetMonth.Month()

	// Try to create a date with the target year, target month, and originalDay
	candidate := time.Date(targetYear, targetMonth, originalDay, t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), t.Location())

	// If the candidate date's month is different from the target month,
	// it means we overflowed (e.g., Feb 31 became Mar 3)
	if candidate.Month() != targetMonth {
		// Go to the first day of the overflow month, then subtract one day
		// to get the last day of the target month
		firstDayOfOverflowMonth := time.Date(candidate.Year(), candidate.Month(), 1, t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), t.Location())
		return firstDayOfOverflowMonth.AddDate(0, 0, -1)
	}

	return candidate
}

// GenerateTimeGroups generates consecutive and evenly distributed time groups
// between startTime and endTime (inclusive) using the specified grouping.
// Each TimeGroup represents a time span where its startTime < time <= endTime.
// The function works backwards from endTime to ensure recent periods are complete.
func GenerateTimeGroups(startTime, endTime time.Time, grouping *TimeGrouping) ([]TimeGroup, error) {
	// Validate grouping parameters
	if !grouping.IsValid() {
		return nil, fmt.Errorf("invalid grouping parameters")
	}

	// Validate time range
	if startTime != endTime && !startTime.Before(endTime) {
		return nil, fmt.Errorf("startTime must be before endTime")
	}

	// Remember the original day of the end time for monthly calculations
	originalDay := endTime.Day()

	// Work backwards from endTime to generate period boundaries
	var timeGroups []TimeGroup
	currentPeriodEnd := endTime

	// Safety counter to prevent infinite loops or too many groups
	maxIterations := 1000
	iterations := 0

	for {
		iterations++
		if iterations > maxIterations {
			return nil, fmt.Errorf("too many iterations in time group generation, possible infinite loop")
		}

		// Calculate the previous period end (which becomes the start of current period)
		var previousPeriodEnd time.Time
		switch grouping.Unit {
		case TimeGroupingDay:
			previousPeriodEnd = currentPeriodEnd.AddDate(0, 0, -grouping.Length)
		case TimeGroupingWeek:
			previousPeriodEnd = currentPeriodEnd.AddDate(0, 0, -7*grouping.Length)
		case TimeGroupingMonth:
			previousPeriodEnd = addMonthsWithDayAdjustment(originalDay, currentPeriodEnd, -grouping.Length)
		default:
			return nil, fmt.Errorf("invalid group_unit: %s", grouping.Unit)
		}

		timeGroups = append(timeGroups, TimeGroup{
			StartTime: previousPeriodEnd,
			EndTime:   currentPeriodEnd,
		})

		// If the previous period end is before startTime, we're done
		if previousPeriodEnd.Before(startTime) {
			break
		}

		currentPeriodEnd = previousPeriodEnd
	}

	// Reverse the timeGroups to get chronological order
	for i, j := 0, len(timeGroups)-1; i < j; i, j = i+1, j-1 {
		timeGroups[i], timeGroups[j] = timeGroups[j], timeGroups[i]
	}

	return timeGroups, nil
}

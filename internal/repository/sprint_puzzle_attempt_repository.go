package repository

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type SprintPuzzleAttemptRepository struct {
	db *gorm.DB
}

func NewSprintPuzzleAttemptRepository(db *gorm.DB) ISprintPuzzleAttemptRepository {
	return &SprintPuzzleAttemptRepository{db: db}
}

// Create creates a new sprint puzzle attempt
func (r *SprintPuzzleAttemptRepository) Create(ctx context.Context, attempt *models.SprintPuzzleAttempt) error {
	if attempt.ID == "" {
		attempt.ID = uuid.New().String()
	}
	now := time.Now()
	attempt.CreatedAt = now
	attempt.UpdatedAt = now
	return r.db.WithContext(ctx).Create(attempt).Error
}

// CreateBatch creates multiple sprint puzzle attempts in a single transaction
func (r *SprintPuzzleAttemptRepository) CreateBatch(ctx context.Context, attempts []models.SprintPuzzleAttempt) error {
	if len(attempts) == 0 {
		return nil
	}

	now := time.Now()
	for i := range attempts {
		if attempts[i].ID == "" {
			attempts[i].ID = uuid.New().String()
		}
		attempts[i].CreatedAt = now
		attempts[i].UpdatedAt = now
	}

	return r.db.WithContext(ctx).CreateInBatches(attempts, 100).Error
}

// GetBySprintAndUser retrieves all attempts for a specific sprint and user
func (r *SprintPuzzleAttemptRepository) GetBySprintAndUser(ctx context.Context, sprintID string, userID string) ([]models.SprintPuzzleAttempt, error) {
	var attempts []models.SprintPuzzleAttempt
	err := r.db.WithContext(ctx).
		Where("sprint_id = ? AND user_id = ?", sprintID, userID).
		Order("sequence_in_sprint ASC").
		Find(&attempts).Error
	return attempts, err
}

// GetBySprintUserAndPuzzle retrieves a specific attempt for a sprint, user, and puzzle
func (r *SprintPuzzleAttemptRepository) GetBySprintUserAndPuzzle(ctx context.Context, sprintID string, userID string, puzzleID string) (*models.SprintPuzzleAttempt, error) {
	var attempt models.SprintPuzzleAttempt
	err := r.db.WithContext(ctx).
		Where("sprint_id = ? AND user_id = ? AND lichess_puzzle_id = ?", sprintID, userID, puzzleID).
		First(&attempt).Error
	if err != nil {
		return nil, err
	}
	return &attempt, nil
}

// GetFailedAttempts retrieves all failed attempts for a specific sprint and user
func (r *SprintPuzzleAttemptRepository) GetFailedAttempts(ctx context.Context, sprintID string, userID string) ([]models.SprintPuzzleAttempt, error) {
	var attempts []models.SprintPuzzleAttempt
	err := r.db.WithContext(ctx).
		Where("sprint_id = ? AND user_id = ? AND was_correct = ?", sprintID, userID, false).
		Order("sequence_in_sprint ASC").
		Find(&attempts).Error
	return attempts, err
}

// Update updates an existing sprint puzzle attempt
func (r *SprintPuzzleAttemptRepository) Update(ctx context.Context, attempt *models.SprintPuzzleAttempt) error {
	attempt.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(attempt).Error
}

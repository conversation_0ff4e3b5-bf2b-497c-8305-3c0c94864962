package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

// LearningQueueRepository implements ILearningQueueRepository for database operations.
type LearningQueueRepository struct {
	db                     *gorm.DB
	learningDailyStatsRepo IUserLearningDailyStatsRepository
}

// NewLearningQueueRepository creates a new instance of LearningQueueRepository.
func NewLearningQueueRepository(db *gorm.DB, learningDailyStatsRepo IUserLearningDailyStatsRepository) ILearningQueueRepository {
	return &LearningQueueRepository{
		db:                     db,
		learningDailyStatsRepo: learningDailyStatsRepo,
	}
}

// AddPuzzlesToQueue adds failed puzzles to the user's learning queue
func (r *LearningQueueRepository) AddPuzzlesToQueue(ctx context.Context, userID string, puzzles []models.LearningQueueEntry) (int, error) {
	if len(puzzles) == 0 {
		return 0, nil
	}

	// Set common fields for all puzzles
	now := time.Now()
	for i := range puzzles {
		puzzles[i].UserID = userID
		// Set DueAt to now if not already set
		if puzzles[i].DueAt.IsZero() {
			puzzles[i].DueAt = now // Due today
		}
		puzzles[i].CreatedAt = now
		puzzles[i].UpdatedAt = now
	}

	// Use CreateInBatches to handle potential unique constraint violations gracefully
	result := r.db.WithContext(ctx).CreateInBatches(puzzles, 100)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to add puzzles to learning queue: %w", result.Error)
	}

	return int(result.RowsAffected), nil
}

// GetDuePuzzles retrieves due puzzles for a user, optionally filtered by attempt type
func (r *LearningQueueRepository) GetDuePuzzles(ctx context.Context, userID string, attemptType *string, limit int) ([]models.LearningQueueItem, error) {
	var queueEntries []models.LearningQueueEntry

	query := r.db.WithContext(ctx).
		Where("user_id = ? AND due_at <= ?", userID, time.Now()).
		Preload("LichessPuzzle")

	// Apply attempt_type filter if specified
	if attemptType != nil && *attemptType != "" {
		query = query.Where("failed_attempt_type = ?", *attemptType)
	}

	// Apply limit
	if limit <= 0 {
		limit = 10 // Default limit
	}

	err := query.Order("due_at ASC").Limit(limit).Find(&queueEntries).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get due puzzles: %w", err)
	}

	// Convert to response format
	items := make([]models.LearningQueueItem, len(queueEntries))
	for i, entry := range queueEntries {
		items[i] = models.LearningQueueItem{
			QueueID:            entry.ID,
			LichessPuzzleID:    entry.LichessPuzzleID,
			FailedAttemptType:  entry.FailedAttemptType,
			DueAt:              entry.DueAt,
			AttemptsSinceAdded: entry.AttemptsSinceAdded,
			ConsecutiveCorrect: entry.ConsecutiveCorrect,
			OriginalSprintID:   entry.OriginalSprintID,
			PuzzleData:         &entry.LichessPuzzle,
		}
	}

	return items, nil
}

// UpdateAfterAttempt updates a learning queue entry after an attempt
func (r *LearningQueueRepository) UpdateAfterAttempt(ctx context.Context, userID, lichessPuzzleID string, wasCorrect bool, attemptType string, timeSpent int) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var entry models.LearningQueueEntry
		err := tx.Where("user_id = ? AND lichess_puzzle_id = ?", userID, lichessPuzzleID).First(&entry).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Puzzle not in learning queue, nothing to update
				return nil
			}
			return fmt.Errorf("failed to find learning queue entry: %w", err)
		}

		// Update attempt counters
		entry.AttemptsSinceAdded++
		wasMastered := false

		// Apply spaced repetition logic
		if wasCorrect {
			entry.ConsecutiveCorrect++

			// If mastered (5 consecutive correct), remove from queue
			if entry.ConsecutiveCorrect >= 5 {
				wasMastered = true
				err := tx.Delete(&entry).Error
				if err != nil {
					return err
				}
			} else {
				// Schedule for future review based on consecutive correct count
				switch entry.ConsecutiveCorrect {
				case 1:
					entry.DueAt = time.Now().Add(2 * 24 * time.Hour) // 2 days
				case 2:
					entry.DueAt = time.Now().Add(4 * 24 * time.Hour) // 4 days
				case 3:
					entry.DueAt = time.Now().Add(7 * 24 * time.Hour) // 7 days
				case 4:
					entry.DueAt = time.Now().Add(15 * 24 * time.Hour) // 15 days
				}
				// Save the updated entry
				err := tx.Save(&entry).Error
				if err != nil {
					return err
				}
			}
		} else {
			// Reset consecutive correct and schedule for tomorrow
			entry.ConsecutiveCorrect = 0
			entry.DueAt = time.Now().Add(24 * time.Hour) // 24 hours
			// Save the updated entry
			err := tx.Save(&entry).Error
			if err != nil {
				return err
			}
		}

		// Track daily stats (outside transaction to avoid deadlocks)
		now := time.Now()

		// Track the learning attempt
		if r.learningDailyStatsRepo != nil {
			err := r.learningDailyStatsRepo.IncrementLearningAttempt(ctx, userID, now, wasCorrect, attemptType, timeSpent)
			if err != nil {
				// Log error but don't fail the transaction
				fmt.Printf("Failed to track learning attempt stats: %v\n", err)
			}

			// Track puzzle mastery if applicable
			if wasMastered {
				err := r.learningDailyStatsRepo.IncrementPuzzlesMastered(ctx, userID, now)
				if err != nil {
					// Log error but don't fail the transaction
					fmt.Printf("Failed to track puzzle mastery stats: %v\n", err)
				}
			}
		}

		return nil
	})
}

// ExistsInQueue checks if a puzzle exists in the user's learning queue
func (r *LearningQueueRepository) ExistsInQueue(ctx context.Context, userID, lichessPuzzleID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.LearningQueueEntry{}).
		Where("user_id = ? AND lichess_puzzle_id = ?", userID, lichessPuzzleID).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check if puzzle exists in learning queue: %w", err)
	}

	return count > 0, nil
}

// RemoveFromQueue removes a puzzle from the queue (when mastered)
func (r *LearningQueueRepository) RemoveFromQueue(ctx context.Context, userID, lichessPuzzleID string) error {
	result := r.db.WithContext(ctx).
		Where("user_id = ? AND lichess_puzzle_id = ?", userID, lichessPuzzleID).
		Delete(&models.LearningQueueEntry{})

	if result.Error != nil {
		return fmt.Errorf("failed to remove puzzle from learning queue: %w", result.Error)
	}

	return nil
}

// GetQueueStats retrieves learning queue statistics for a user
func (r *LearningQueueRepository) GetQueueStats(ctx context.Context, userID string) (*models.LearningQueueStats, error) {
	stats := &models.LearningQueueStats{}

	// Get total queued count
	err := r.db.WithContext(ctx).
		Model(&models.LearningQueueEntry{}).
		Where("user_id = ?", userID).
		Count(&stats.TotalQueued).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get total queued count: %w", err)
	}

	// Get due today count
	today := time.Now().Truncate(24 * time.Hour).Add(24*time.Hour - time.Nanosecond)
	err = r.db.WithContext(ctx).
		Model(&models.LearningQueueEntry{}).
		Where("user_id = ? AND due_at <= ?", userID, today).
		Count(&stats.DueToday).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get due today count: %w", err)
	}

	// Get regular puzzles count
	err = r.db.WithContext(ctx).
		Model(&models.LearningQueueEntry{}).
		Where("user_id = ? AND failed_attempt_type = ?", userID, "regular").
		Count(&stats.RegularPuzzles).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get regular puzzles count: %w", err)
	}

	// Get arrow duel puzzles count
	err = r.db.WithContext(ctx).
		Model(&models.LearningQueueEntry{}).
		Where("user_id = ? AND failed_attempt_type = ?", userID, "arrow_duel").
		Count(&stats.ArrowDuelPuzzles).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get arrow duel puzzles count: %w", err)
	}

	// TODO: Implement daily retries and mastery rate calculations
	// These would require additional queries to UserLearningDailyStats
	stats.DailyRetriesToday = 0
	stats.DailyRetriesThisWeek = 0
	stats.MasteryRate = 0.0

	return stats, nil
}

// GetByUserIDAndPuzzleID retrieves a specific learning queue entry
func (r *LearningQueueRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID, lichessPuzzleID string) (*models.LearningQueueEntry, error) {
	var entry models.LearningQueueEntry
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND lichess_puzzle_id = ?", userID, lichessPuzzleID).
		First(&entry).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get learning queue entry: %w", err)
	}

	return &entry, nil
}

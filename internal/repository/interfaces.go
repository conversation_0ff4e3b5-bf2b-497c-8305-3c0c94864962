package repository

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"gorm.io/gorm"
)

var (
	// ErrNotFound is the error returned when a resource is not found.
	ErrNotFound = gorm.ErrRecordNotFound
)

// IUserRepository defines the interface for user repository operations
type IUserRepository interface {
	Create(ctx context.Context, user *models.User) error
	GetByID(ctx context.Context, id string) (*models.User, error)
	GetByEmail(ctx context.Context, email string) (*models.User, error)
	GetAll(ctx context.Context) ([]models.User, error)
	Update(ctx context.Context, user *models.User) error
	Delete(ctx context.Context, id string) error
	UpdateLastSignIn(ctx context.Context, id string) error

	// Firebase authentication methods
	GetByFirebaseUID(ctx context.Context, firebaseUID string) (*models.User, error)
	GetOrCreateFirebaseUser(ctx context.Context, firebaseUID, email string) (*models.User, bool, error)

	// ChessProfile specific methods
	AddChessProfile(ctx context.Context, userID string, profile *models.ChessProfile) error
	GetChessProfilesByUserID(ctx context.Context, userID string) ([]models.ChessProfile, error)
	GetChessProfileByID(ctx context.Context, profileID string) (*models.ChessProfile, error)
	GetChessProfileByPlatform(ctx context.Context, userID string, platform string) (*models.ChessProfile, error)
	UpdateChessProfile(ctx context.Context, profile *models.ChessProfile) error
	UpdateChessProfileFields(ctx context.Context, profileID string, updates map[string]interface{}) error
	DeleteChessProfile(ctx context.Context, userID string, profileID string) error
	CountChessProfiles(ctx context.Context, userID string) (int64, error)
}

// IGameRepository defines the interface for game repository operations
type IGameRepository interface {
	Create(ctx context.Context, game *models.Game) error
	GetByID(ctx context.Context, id string) (*models.Game, error)
	GetByIDWithoutPGN(ctx context.Context, id string) (*models.Game, error)
	Update(ctx context.Context, game *models.Game) error
	Delete(ctx context.Context, id string) error

	// DeleteByFilter deletes games for a specific user that match the given filter.
	// Returns the number of games deleted.
	DeleteByFilter(ctx context.Context, userID string, filter GameFilter) (int64, error)

	// ListByUserID retrieves a paginated list of games for a specific user,
	// optionally filtered by chess profile ID and game time range.
	// Returns the list of games and the total count matching the criteria.
	ListByUserID(ctx context.Context, userID string, filter GameFilter, offset int, limit int) ([]models.Game, int64, error)

	// GetGameStats retrieves statistics about games for a specific user,
	// optionally filtered by the same criteria as ListByUserID.
	// If offset and limit are provided, stats will be calculated only for the specified games.
	// If grouping is provided, stats will be grouped by time periods.
	// Returns an array of GameStats structs, one for each time period (or a single-element array if no grouping is requested).
	GetGameStats(ctx context.Context, userID string, filter GameFilter, offset int, limit int, grouping *common.TimeGrouping) ([]*models.GameStats, error)
}

// GameFilter holds criteria for filtering games. Zero values are ignored.
type GameFilter struct {
	ChessProfileID *string               // Optional: Keep for context, but filtering uses Platform/Username
	Platform       *models.ChessPlatform // Optional: Filter by platform
	ChessUsername  *string               // Optional: Filter by username on that platform
	StartTime      *time.Time            // Optional: Filter games played after this time
	EndTime        *time.Time            // Optional: Filter games played before this time
	UserColor      *models.Color         // Optional: Filter by user's color
	TimeControl    *string               // Optional: Filter by time control
	Rated          *bool                 // Optional: Filter by rated status
	Result         *models.GameResult    // Optional: Filter by game result
	OmitPGN        bool                  // Optional: If true, don't load the CompressedPGN field
}

// IPuzzleRepository defines the interface for puzzle repository operations
type IPuzzleRepository interface {
	Create(ctx context.Context, puzzle *models.Puzzle) error
	GetByID(ctx context.Context, id string) (*models.Puzzle, error)
	GetByGameID(ctx context.Context, gameID string) ([]models.Puzzle, error)
	Update(ctx context.Context, puzzle *models.Puzzle) error
	Delete(ctx context.Context, id string) error

	// ListByUserID retrieves a paginated list of puzzles for a specific user,
	// optionally filtered by tags and the associated game's time range.
	// Returns the list of puzzles and the total count matching the criteria.
	ListByUserID(ctx context.Context, userID string, filter PuzzleFilter, offset int, limit int) ([]models.Puzzle, int64, error)

	// GetPuzzleStats retrieves statistics about puzzles for a specific user,
	// optionally filtered by the same criteria as ListByUserID.
	// If offset and limit are provided, stats will be calculated only for the specified puzzles.
	// If grouping is provided, stats will be grouped by time periods.
	// Returns an array of PuzzleStats structs, one for each time period (or a single-element array if no grouping is requested).
	GetPuzzleStats(ctx context.Context, userID string, filter PuzzleFilter, offset int, limit int, grouping *common.TimeGrouping) ([]*models.PuzzleStats, error)

	// GetRandomPuzzles retrieves random user puzzles for practice based on the provided filter
	// Note: User puzzles don't have explicit ratings, so this uses puzzle difficulty metrics (length)
	GetRandomPuzzles(ctx context.Context, userID string, filter common.UserPuzzleFilter, limit int) ([]models.Puzzle, error)
}

// PuzzleFilter holds criteria for filtering puzzles. Zero values/empty slices are ignored.
type PuzzleFilter struct {
	Tags          []string             // Optional: Filter by tags (match any with OR relation)
	GameStartTime *time.Time           // Optional: Filter puzzles from games played after this time
	GameEndTime   *time.Time           // Optional: Filter puzzles from games played before this time
	Themes        []models.PuzzleTheme // Optional: Filter by puzzle themes (match any with OR relation)
	UserColor     *models.Color        // Optional: Filter by user's color
	PuzzleColor   *models.Color        // Optional: Filter by puzzle color
	GameMoveMin   *int                 // Optional: Filter by minimum game move
	GameMoveMax   *int                 // Optional: Filter by maximum game move
	PrevCpMin     *int                 // Optional: Filter by minimum previous centipawn value
	PrevCpMax     *int                 // Optional: Filter by maximum previous centipawn value
	CpChangeMin   *int                 // Optional: Filter by minimum centipawn change
	CpChangeMax   *int                 // Optional: Filter by maximum centipawn change
	TimeControl   *string              // Optional: Filter by game time control
	Rated         *bool                // Optional: Filter by game rated status
}

// ITaskRepository defines the interface for task repository operations
type ITaskRepository interface {
	Create(ctx context.Context, task *models.Task) error
	GetByID(ctx context.Context, taskID string) (*models.Task, error)
	ListByUserID(ctx context.Context, userID string, statuses []models.TaskStatus) ([]models.Task, error)
	// ClaimNextPending finds the oldest pending task, updates its status to in_progress,
	// assigns the workerID, sets the pickedUpAt timestamp, and returns the claimed task.
	// Returns nil, nil if no pending task is available.
	ClaimNextPending(ctx context.Context, workerID string) (*models.Task, error)
	// Update updates an existing task. It uses optimistic locking based on the
	// expectedUpdatedAt timestamp. If the task's UpdatedAt in the database does not
	// match expectedUpdatedAt, an error should be returned.
	Update(ctx context.Context, task *models.Task, expectedUpdatedAt time.Time) error
	// ResetHanging finds tasks that have been in 'in_progress' status for longer than
	// the specified timeout, resets their status to 'pending', clears workerID and pickedUpAt,
	// increments the attempt count, and returns the number of tasks reset.
	ResetHanging(ctx context.Context, timeout time.Duration) (int64, error)
	// ListAll retrieves a paginated list of tasks, optionally filtered by status.
	// It returns the list of tasks and the total count of tasks matching the filter.
	ListAll(ctx context.Context, userID *string, statuses []models.TaskStatus, offset int, limit int) ([]models.Task, int64, error)
	// DeleteOldTasks removes tasks older than the specified cutoff time that match the
	// given statuses, up to a specified limit. Returns the number of tasks deleted.
	DeleteOldTasks(ctx context.Context, cutoff time.Time, statuses []models.TaskStatus, limit int) (int64, error)
}

// IIdempotencyRepository defines the interface for managing idempotency records.
type IIdempotencyRepository interface {
	// Get retrieves an idempotency record by its key, user ID, request method, and request path.
	// It should return ErrNotFound if no matching record exists.
	Get(ctx context.Context, idempotencyKey string, userID string, requestMethod string, requestPath string) (*models.IdempotencyRecord, error)

	// Create stores a new idempotency record.
	// It should handle potential race conditions or duplicate key errors gracefully
	// (e.g., by returning an error or potentially updating if allowed, though
	// typically creation should fail if the key already exists for the user).
	Create(ctx context.Context, record *models.IdempotencyRecord) error

	// DeleteExpired removes idempotency records whose ExpiresAt time has passed.
	// Returns the number of records deleted and any error encountered.
	DeleteExpired(ctx context.Context) (int64, error)
}

// IInvitationCodeRepository defines the interface for invitation code repository operations
type IInvitationCodeRepository interface {
	// Create creates a new invitation code
	Create(ctx context.Context, code *models.InvitationCode) error

	// GetByID retrieves an invitation code by ID
	GetByID(ctx context.Context, id string) (*models.InvitationCode, error)

	// GetByCode retrieves an invitation code by its code string
	GetByCode(ctx context.Context, code string) (*models.InvitationCode, error)

	// Update updates an invitation code (e.g., to mark it as used)
	Update(ctx context.Context, code *models.InvitationCode) error

	// ListValid retrieves a paginated list of valid (unused and not expired) invitation codes
	ListValid(ctx context.Context, offset int, limit int) ([]models.InvitationCode, int64, error)

	// ListAll retrieves a paginated list of all invitation codes
	ListAll(ctx context.Context, offset int, limit int) ([]models.InvitationCode, int64, error)
}

// ISessionTokenRepository defines the interface for session token repository operations
type ISessionTokenRepository interface {
	// Create creates a new session token
	Create(ctx context.Context, token *models.SessionToken) error

	// GetByToken retrieves a session token by its token string
	GetByToken(ctx context.Context, token string) (*models.SessionToken, error)

	// GetByID retrieves a session token by its ID
	GetByID(ctx context.Context, id string) (*models.SessionToken, error)

	// ListByUserID retrieves all session tokens for a specific user
	ListByUserID(ctx context.Context, userID string) ([]models.SessionToken, error)

	// Update updates a session token (e.g., to extend expiration)
	Update(ctx context.Context, token *models.SessionToken) error

	// Delete deletes a session token by ID
	Delete(ctx context.Context, id string) error

	// DeleteByUserID deletes all session tokens for a specific user
	DeleteByUserID(ctx context.Context, userID string) error

	// DeleteExpired removes session tokens that have expired
	DeleteExpired(ctx context.Context) (int64, error)
}

// IEventRepository defines the interface for event repository operations
type IEventRepository interface {
	// Create creates a new event. The event ID acts as an idempotency key.
	// If an event with the same ID already exists, returns an error.
	Create(ctx context.Context, event *models.Event) error

	// GetByID retrieves an event by its ID
	GetByID(ctx context.Context, id string) (*models.Event, error)

	// ListByUserID retrieves a paginated list of events for a specific user,
	// optionally filtered by event type and time range.
	// Returns the list of events and the total count matching the criteria.
	ListByUserID(ctx context.Context, userID string, filter EventFilter, offset int, limit int) ([]models.Event, int64, error)
}

// EventFilter defines filtering options for event queries
type EventFilter struct {
	EventTypes    []models.EventType    `json:"event_types,omitempty"`     // Filter by event types
	EventSubTypes []models.EventSubType `json:"event_sub_types,omitempty"` // Filter by event sub-types
	StartTime     *time.Time            `json:"start_time,omitempty"`      // Filter events after this time
	EndTime       *time.Time            `json:"end_time,omitempty"`        // Filter events before this time
}

// IUserDailyStatsRepository defines the interface for user daily stats repository operations
type IUserDailyStatsRepository interface {
	// ListByUserID retrieves a paginated list of daily stats for a specific user,
	// optionally filtered by date range.
	// Returns the list of daily stats and the total count matching the criteria.
	ListByUserID(ctx context.Context, userID string, startDate *time.Time, endDate *time.Time, offset int, limit int) ([]models.UserDailyStats, int64, error)
}

// IUserPuzzleStatsRepository defines the interface for user puzzle stats repository operations
type IUserPuzzleStatsRepository interface {
	// GetByUserID retrieves all puzzle stats for a specific user
	GetByUserID(ctx context.Context, userID string) ([]models.UserPuzzleStats, error)

	// GetByUserIDAndPuzzleID retrieves puzzle stats for a specific user and puzzle
	GetByUserIDAndPuzzleID(ctx context.Context, userID string, puzzleID string) (*models.UserPuzzleStats, error)

	// GetDislikedPuzzleIDs retrieves all disliked puzzle IDs for a specific user
	GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error)
}

// IUserPuzzleArrowDuelStatsRepository defines the interface for user puzzle arrow-duel stats repository operations
type IUserPuzzleArrowDuelStatsRepository interface {
	// GetByUserID retrieves all arrow-duel puzzle stats for a specific user
	GetByUserID(ctx context.Context, userID string) ([]models.UserPuzzleArrowDuelStats, error)

	// GetByUserIDAndPuzzleID retrieves arrow-duel stats for a specific user and puzzle
	GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.UserPuzzleArrowDuelStats, error)

	// GetDislikedPuzzleIDs retrieves all disliked puzzle IDs for a specific user (arrow-duel mode)
	GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error)
}

// IUserLichessPuzzleArrowDuelStatsRepository defines the interface for user lichess puzzle arrow-duel stats repository operations
type IUserLichessPuzzleArrowDuelStatsRepository interface {
	// GetByUserIDAndPuzzleID retrieves arrow-duel stats for a specific user and lichess puzzle
	GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.UserLichessPuzzleArrowDuelStats, error)

	// GetDislikedPuzzleIDs retrieves all disliked lichess puzzle IDs for a specific user (arrow-duel mode)
	GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error)
}

// IUserEloRepository defines the interface for user ELO repository operations
type IUserEloRepository interface {
	// GetByUserIDAndEloType retrieves ELO rating for a specific user and ELO type
	GetByUserIDAndEloType(ctx context.Context, userID string, eloType string) (*models.UserElo, error)

	// GetByUserID retrieves all ELO ratings for a specific user
	GetByUserID(ctx context.Context, userID string) ([]models.UserElo, error)

	// Save creates or updates a user's ELO rating for a specific type
	Save(ctx context.Context, userElo *models.UserElo) error
}

// ISprintRepository defines the interface for sprint repository operations
type ISprintRepository interface {
	Create(ctx context.Context, sprint *models.Sprint) error
	GetByID(ctx context.Context, id string) (*models.Sprint, error)
	Update(ctx context.Context, sprint *models.Sprint) error
	ListByUserID(ctx context.Context, userID string, filter SprintFilter, offset int, limit int) ([]models.Sprint, int64, error)
	GetSprintStats(ctx context.Context, userID string, filter SprintFilter, offset int, limit int, grouping *common.TimeGrouping) ([]*models.SprintStats, error)
	MarkAbandonedSprints(ctx context.Context, cutoffTime time.Time, limit int) (int64, error)
}

// SprintFilter defines filtering options for sprint queries
type SprintFilter struct {
	StartTime *time.Time
	EndTime   *time.Time
	Status    []models.SprintStatus
	EloType   *string
}

// SprintPuzzleAttemptStatus represents the status of a puzzle attempt in a sprint
type SprintPuzzleAttemptStatus string

const (
	SprintPuzzleStatusUnattempted SprintPuzzleAttemptStatus = "unattempted"
	SprintPuzzleStatusSolved      SprintPuzzleAttemptStatus = "solved"
	SprintPuzzleStatusFailed      SprintPuzzleAttemptStatus = "failed"
	SprintPuzzleStatusAttempted   SprintPuzzleAttemptStatus = "attempted" // Both solved and failed
)

// SprintPuzzleFilter defines filtering options for sprint puzzle queries
type SprintPuzzleFilter struct {
	Status      *SprintPuzzleAttemptStatus // Filter by attempt status
	SequenceMin *int                       // Minimum sequence number in sprint
	SequenceMax *int                       // Maximum sequence number in sprint
	AttemptType *string                    // Filter by attempt type (regular, arrow_duel)
}

// SprintPuzzleWithAttempt represents a sprint puzzle with attempt information
type SprintPuzzleWithAttempt struct {
	SprintPuzzle  models.SprintPuzzle
	LichessPuzzle models.LichessPuzzle
	AttemptStatus SprintPuzzleAttemptStatus
	UserMoves     []string
	WasCorrect    *bool
	TimeTakenMs   *int
	AttemptedAt   *time.Time

	// Arrow-duel specific fields
	AttemptType    *string
	CandidateMoves []string
	ChosenMove     *string
}

// ISprintPuzzleRepository defines the interface for sprint puzzle repository operations
type ISprintPuzzleRepository interface {
	Create(ctx context.Context, sprintPuzzle *models.SprintPuzzle) error
	CreateBatch(ctx context.Context, sprintPuzzles []models.SprintPuzzle) error
	GetBySprintID(ctx context.Context, sprintID string) ([]models.SprintPuzzle, error)
	GetByID(ctx context.Context, id string) (*models.SprintPuzzle, error)
	CountBySprintID(ctx context.Context, sprintID string) (int, error)

	// GetSprintPuzzlesWithAttempts retrieves sprint puzzles with attempt information
	GetSprintPuzzlesWithAttempts(ctx context.Context, sprintID string, userID string, filter SprintPuzzleFilter, offset int, limit int) ([]SprintPuzzleWithAttempt, int64, error)
}

// ISprintPuzzleAttemptRepository defines the interface for sprint puzzle attempt repository operations
type ISprintPuzzleAttemptRepository interface {
	Create(ctx context.Context, attempt *models.SprintPuzzleAttempt) error
	CreateBatch(ctx context.Context, attempts []models.SprintPuzzleAttempt) error
	GetBySprintAndUser(ctx context.Context, sprintID string, userID string) ([]models.SprintPuzzleAttempt, error)
	GetBySprintUserAndPuzzle(ctx context.Context, sprintID string, userID string, puzzleID string) (*models.SprintPuzzleAttempt, error)
	Update(ctx context.Context, attempt *models.SprintPuzzleAttempt) error
}

// IEloHistoryRepository defines the interface for elo history repository operations
type IEloHistoryRepository interface {
	Create(ctx context.Context, eloHistory *models.EloHistory) error
	ListByUserID(ctx context.Context, userID string, eloType *string, offset int, limit int) ([]models.EloHistory, int64, error)
}

// IUserSprintDailyStatsRepository defines the interface for user sprint daily stats repository operations
type IUserSprintDailyStatsRepository interface {
	// ListByUserID retrieves a paginated list of daily sprint stats for a specific user,
	// optionally filtered by date range and elo type.
	// Returns the list of daily stats and the total count matching the criteria.
	ListByUserID(ctx context.Context, userID string, eloType *string, startDate *time.Time, endDate *time.Time, offset int, limit int) ([]models.UserSprintDailyStats, int64, error)
}

// IPuzzleQueueRepository defines the interface for puzzle queue repository operations
type IPuzzleQueueRepository interface {
	// AddPuzzlesToQueue adds puzzles to the user's queue
	AddPuzzlesToQueue(ctx context.Context, userID string, puzzles []models.PuzzleQueueEntry) (int, error)

	// GetDuePuzzles retrieves due puzzles for a user, optionally filtered by mistake type
	GetDuePuzzles(ctx context.Context, userID string, mistakeBy *string, limit int) ([]models.PuzzleQueueItem, error)

	// UpdateAfterAttempt updates a puzzle queue entry after an attempt
	UpdateAfterAttempt(ctx context.Context, userID, puzzleID string, wasCorrect bool) error

	// GetRecentPuzzlesNotInQueue retrieves recent puzzles not already in the queue
	GetRecentPuzzlesNotInQueue(ctx context.Context, userID string, mistakeBy *string, limit int) ([]string, error)

	// RemoveFromQueue removes a puzzle from the queue (when mastered)
	RemoveFromQueue(ctx context.Context, userID, puzzleID string) error

	// GetQueueStats retrieves queue statistics for a user
	GetQueueStats(ctx context.Context, userID string) (*models.PuzzleQueueStats, error)

	// GetByUserIDAndPuzzleID retrieves a specific queue entry
	GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.PuzzleQueueEntry, error)
}

// Ensure repositories implement interfaces
var _ IUserRepository = (*UserRepository)(nil)

// var _ IGameRepository = (*GameRepository)(nil) // Temporarily commented out until ListByUserID is implemented
// var _ IPuzzleRepository = (*PuzzleRepository)(nil) // Temporarily commented out until ListByUserID is implemented
var _ ITaskRepository = (*TaskRepository)(nil)
var _ IIdempotencyRepository = (*IdempotencyRepository)(nil)

var _ IInvitationCodeRepository = (*InvitationCodeRepository)(nil)
var _ ISessionTokenRepository = (*SessionTokenRepository)(nil)
var _ IEventRepository = (*EventRepository)(nil)
var _ IUserDailyStatsRepository = (*UserDailyStatsRepository)(nil)
var _ IUserPuzzleStatsRepository = (*UserPuzzleStatsRepository)(nil)
var _ IUserLichessPuzzleStatsRepository = (*UserLichessPuzzleStatsRepository)(nil)

var _ IUserPuzzleArrowDuelStatsRepository = (*UserPuzzleArrowDuelStatsRepository)(nil)
var _ IUserLichessPuzzleArrowDuelStatsRepository = (*UserLichessPuzzleArrowDuelStatsRepository)(nil)
var _ IUserEloRepository = (*UserEloRepository)(nil)
var _ ISprintRepository = (*SprintRepository)(nil)
var _ ISprintPuzzleRepository = (*SprintPuzzleRepository)(nil)
var _ IEloHistoryRepository = (*EloHistoryRepository)(nil)
var _ IUserSprintDailyStatsRepository = (*UserSprintDailyStatsRepository)(nil)
var _ IPuzzleQueueRepository = (*PuzzleQueueRepository)(nil)

package models

import (
	"time"
)

// UserPuzzleStats represents per-puzzle statistics for a user (for user-generated puzzles)
type UserPuzzleStats struct {
	ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_puzzle,unique;index:idx_user_last_attempt;index:idx_user_puzzle_disliked,composite:is_disliked" json:"user_id"`
	PuzzleID           string     `gorm:"type:varchar(255);not null;index:idx_user_puzzle,unique" json:"puzzle_id"`
	Attempts           int        `gorm:"default:0" json:"attempts"`
	SuccessCount       int        `gorm:"default:0" json:"success_count"`
	TotalTime          int        `gorm:"default:0" json:"total_time"`   // Total time spent in seconds
	AverageTime        float64    `gorm:"default:0" json:"average_time"` // Average time per attempt
	LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp;index:idx_user_last_attempt" json:"last_attempt_time"`
	LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
	IsDisliked         bool       `gorm:"default:false;index:idx_user_puzzle_disliked,composite:user_id" json:"is_disliked"` // User marked this puzzle as disliked
	DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`                                                     // When the puzzle was disliked
	CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	Puzzle Puzzle `gorm:"foreignKey:PuzzleID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for UserPuzzleStats
func (UserPuzzleStats) TableName() string {
	return "user_puzzle_stats"
}

// UserDailyStats represents daily puzzle statistics for efficient aggregation
type UserDailyStats struct {
	ID                  string    `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID              string    `gorm:"type:varchar(36);not null;index:idx_user_date,unique" json:"user_id"`
	Date                time.Time `gorm:"type:date;not null;index:idx_user_date,unique" json:"date"`
	PuzzleSuccess       int       `gorm:"default:0" json:"puzzle_success"`        // Number of successful puzzles
	PuzzleTotal         int       `gorm:"default:0" json:"puzzle_total"`          // Total puzzle attempts
	Streak              int       `gorm:"default:0" json:"streak"`                // Consecutive days with puzzle activity
	PuzzleTotalDuration int       `gorm:"default:0" json:"puzzle_total_duration"` // Total time spent in seconds
	CreatedAt           time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt           time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// UserLichessPuzzleStats represents per-lichess-puzzle statistics for a user
type UserLichessPuzzleStats struct {
	ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_lichess_puzzle,unique;index:idx_user_lichess_last_attempt;index:idx_user_lichess_disliked,composite:is_disliked" json:"user_id"`
	LichessPuzzleID    string     `gorm:"type:varchar(10);not null;index:idx_user_lichess_puzzle,unique" json:"lichess_puzzle_id"` // Lichess puzzle ID (e.g., "00008")
	Attempts           int        `gorm:"default:0" json:"attempts"`
	SuccessCount       int        `gorm:"default:0" json:"success_count"`
	TotalTime          int        `gorm:"default:0" json:"total_time"`   // Total time spent in seconds
	AverageTime        float64    `gorm:"default:0" json:"average_time"` // Average time per attempt
	LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp;index:idx_user_lichess_last_attempt" json:"last_attempt_time"`
	LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
	IsDisliked         bool       `gorm:"default:false;index:idx_user_lichess_disliked,composite:user_id" json:"is_disliked"` // User marked this puzzle as disliked
	DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`                                                      // When the puzzle was disliked
	CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User          User          `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:CASCADE" json:"lichess_puzzle"`
}

// TableName returns the table name for UserLichessPuzzleStats
func (UserLichessPuzzleStats) TableName() string {
	return "user_lichess_puzzle_stats"
}

// UserElo represents current ELO rating for a user per ELO type
type UserElo struct {
	ID              int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID          string    `gorm:"type:varchar(36);not null;index:idx_user_elo_type,unique" json:"user_id"`
	EloType         string    `gorm:"type:varchar(100);not null;index:idx_user_elo_type,unique" json:"elo_type"`
	Rating          int       `gorm:"not null" json:"rating"`
	RatingDeviation float64   `gorm:"not null" json:"rating_deviation"` // Glicko-2 RD
	Volatility      float64   `gorm:"not null" json:"volatility"`       // Glicko-2 Sigma
	GamesPlayed     int       `gorm:"not null;default:0" json:"games_played"`
	LastActiveAt    time.Time `gorm:"not null;default:current_timestamp" json:"last_active_at"`
	IsProvisional   bool      `gorm:"not null;default:true" json:"is_provisional"`
	CreatedAt       time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt       time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// UserPuzzleArrowDuelStats represents arrow-duel stats for user puzzles
type UserPuzzleArrowDuelStats struct {
	ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_puzzle_arrow_duel,unique" json:"user_id"`
	PuzzleID           string     `gorm:"type:varchar(255);not null;index:idx_user_puzzle_arrow_duel,unique" json:"puzzle_id"`
	Attempts           int        `gorm:"default:0" json:"attempts"`
	SuccessCount       int        `gorm:"default:0" json:"success_count"`
	TotalTime          int        `gorm:"default:0" json:"total_time"`
	AverageTime        float64    `gorm:"default:0" json:"average_time"`
	LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp" json:"last_attempt_time"`
	LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
	IsDisliked         bool       `gorm:"default:false" json:"is_disliked"`
	DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`
	CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	Puzzle Puzzle `gorm:"foreignKey:PuzzleID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for UserPuzzleArrowDuelStats
func (UserPuzzleArrowDuelStats) TableName() string {
	return "user_puzzle_arrow_duel_stats"
}

// UserLichessPuzzleArrowDuelStats represents arrow-duel stats for Lichess puzzles
type UserLichessPuzzleArrowDuelStats struct {
	ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_lichess_arrow_duel,unique" json:"user_id"`
	LichessPuzzleID    string     `gorm:"type:varchar(10);not null;index:idx_user_lichess_arrow_duel,unique" json:"lichess_puzzle_id"`
	Attempts           int        `gorm:"default:0" json:"attempts"`
	SuccessCount       int        `gorm:"default:0" json:"success_count"`
	TotalTime          int        `gorm:"default:0" json:"total_time"`
	AverageTime        float64    `gorm:"default:0" json:"average_time"`
	LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp" json:"last_attempt_time"`
	LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
	IsDisliked         bool       `gorm:"default:false" json:"is_disliked"`
	DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`
	CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User          User          `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for UserLichessPuzzleArrowDuelStats
func (UserLichessPuzzleArrowDuelStats) TableName() string {
	return "user_lichess_puzzle_arrow_duel_stats"
}

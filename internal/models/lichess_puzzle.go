package models

import (
	"time"

	"github.com/lib/pq"
)

// LichessPuzzle represents a chess puzzle from the Lichess puzzle database
// These are global puzzles shared among all users, not owned by any specific user
type LichessPuzzle struct {
	ID                         string         `gorm:"type:varchar(10);primary_key" json:"id"`                                  // Lichess puzzle ID (e.g., "00008")
	FEN                        string         `gorm:"type:varchar(100);not null" json:"fen"`                                   // FEN notation of the position
	Moves                      pq.StringArray `gorm:"type:text[];not null" json:"moves"`                                       // Solution moves
	Rating                     int            `gorm:"not null;index:idx_lichess_puzzles_rating_id,composite:id" json:"rating"` // Puzzle rating/difficulty
	RatingDeviation            int            `gorm:"not null" json:"rating_deviation"`                                        // Rating deviation
	Popularity                 int            `gorm:"not null" json:"popularity"`                                              // Popularity score
	NbPlays                    int            `gorm:"not null" json:"nb_plays"`                                                // Number of times played
	Themes                     pq.StringArray `gorm:"type:text[];not null;index:idx_lichess_themes,gin" json:"themes"`         // Puzzle themes (e.g., "mate", "fork", etc.)
	GameURL                    string         `gorm:"type:varchar(255)" json:"game_url,omitempty"`                             // URL to the original game
	OpeningTags                pq.StringArray `gorm:"type:text[]" json:"opening_tags,omitempty"`                               // Opening tags if available
	BestMoveEval               *float64       `gorm:"type:decimal(10,2)" json:"best_move_eval,omitempty"`                      // Stockfish evaluation of the best move in centipawns from white's perspective
	BestMove                   string         `gorm:"type:varchar(10)" json:"best_move,omitempty"`                             // Best move in algebraic notation (e.g., "d8a5")
	PositionEvalAfterFirstMove *float64       `gorm:"type:decimal(10,2)" json:"position_eval_after_first_move,omitempty"`      // Stockfish evaluation after the first move in the puzzle solution from white's perspective
	CreatedAt                  time.Time      `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt                  time.Time      `gorm:"not null;default:current_timestamp" json:"updated_at"`
}

// TableName returns the table name for LichessPuzzle
func (LichessPuzzle) TableName() string {
	return "lichess_puzzles"
}

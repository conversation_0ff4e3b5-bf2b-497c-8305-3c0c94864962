package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/lestrrat-go/jwx/v2/jwk"
	"github.com/lestrrat-go/jwx/v2/jwt"
)

// FirebaseTokenClaims represents the claims in a Firebase ID token
type FirebaseTokenClaims struct {
	UserID        string `json:"user_id"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	AuthTime      int64  `json:"auth_time"`
	Firebase      struct {
		Identities struct {
			Email []string `json:"email"`
		} `json:"identities"`
		SignInProvider string `json:"sign_in_provider"`
	} `json:"firebase"`
}

// JWKSCache handles caching of JWKS keys
type JWKSCache struct {
	keys      jwk.Set
	lastFetch time.Time
	ttl       time.Duration
	mutex     sync.RWMutex
}

// GetK<PERSON>s retrieves JWKS keys with caching
func (c *J<PERSON><PERSON>Cache) GetKeys(ctx context.Context, endpoint string) (jwk.Set, error) {
	c.mutex.RLock()
	if time.Since(c.lastFetch) < c.ttl && c.keys != nil {
		defer c.mutex.RUnlock()
		return c.keys, nil
	}
	c.mutex.RUnlock()

	// Fetch new keys with write lock
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Double-check pattern
	if time.Since(c.lastFetch) < c.ttl && c.keys != nil {
		return c.keys, nil
	}

	// Fetch from endpoint
	keys, err := jwk.Fetch(ctx, endpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch JWKS from %s: %w", endpoint, err)
	}

	c.keys = keys
	c.lastFetch = time.Now()
	return keys, nil
}

// FirebaseAuthService handles Firebase authentication token verification
type FirebaseAuthService struct {
	projectID             string
	jwksEndpoint          string
	jwksCache             *JWKSCache
	skipTokenVerification bool
}

// NewFirebaseAuthService creates a new Firebase authentication service
func NewFirebaseAuthService(cfg config.FirebaseConfig) *FirebaseAuthService {
	return &FirebaseAuthService{
		projectID:             cfg.ProjectID,
		jwksEndpoint:          cfg.JWKSEndpoint,
		skipTokenVerification: cfg.SkipTokenVerification,
		jwksCache: &JWKSCache{
			ttl: time.Hour, // Cache JWKS for 1 hour as recommended by Firebase
		},
	}
}

// VerifyIDToken verifies a Firebase ID token and returns the claims
func (s *FirebaseAuthService) VerifyIDToken(ctx context.Context, tokenString string) (*FirebaseTokenClaims, error) {
	var verified jwt.Token
	var err error

	if s.skipTokenVerification {
		// Skip verification for development/testing with Firebase emulator
		// Parse token without verification to extract claims
		verified, err = jwt.ParseString(tokenString, jwt.WithVerify(false))
		if err != nil {
			return nil, fmt.Errorf("failed to parse token: %w", err)
		}
	} else {
		// Get JWKS from Firebase endpoint
		keys, err := s.jwksCache.GetKeys(ctx, s.jwksEndpoint)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch JWKS: %w", err)
		}

		// Verify token according to Firebase requirements
		verified, err = jwt.ParseString(tokenString,
			jwt.WithKeySet(keys),
			jwt.WithValidate(true),
			jwt.WithAudience(s.projectID), // Must match Firebase project ID
			jwt.WithIssuer(fmt.Sprintf("https://securetoken.google.com/%s", s.projectID)), // Firebase issuer
		)
		if err != nil {
			return nil, fmt.Errorf("token verification failed: %w", err)
		}
	}

	// Extract Firebase-specific claims
	claims := &FirebaseTokenClaims{}

	// Get standard claims
	claims.UserID = verified.Subject()
	if emailClaim, ok := verified.Get("email"); ok {
		if email, ok := emailClaim.(string); ok {
			claims.Email = email
		}
	}
	if emailVerifiedClaim, ok := verified.Get("email_verified"); ok {
		if emailVerified, ok := emailVerifiedClaim.(bool); ok {
			claims.EmailVerified = emailVerified
		}
	}
	if authTimeClaim, ok := verified.Get("auth_time"); ok {
		if authTime, ok := authTimeClaim.(float64); ok {
			claims.AuthTime = int64(authTime)
		}
	}

	// Get Firebase-specific claims
	if firebaseClaim, ok := verified.Get("firebase"); ok {
		firebaseBytes, err := json.Marshal(firebaseClaim)
		if err == nil {
			if err := json.Unmarshal(firebaseBytes, &claims.Firebase); err != nil {
				return nil, fmt.Errorf("failed to unmarshal firebase claims: %w", err)
			}
		}
	}

	// Additional Firebase validations (skip some validations when verification is disabled)
	if !s.skipTokenVerification {
		if claims.AuthTime > time.Now().Unix() {
			return nil, fmt.Errorf("auth_time is in the future")
		}
	}

	if claims.UserID == "" {
		return nil, fmt.Errorf("missing user_id claim")
	}

	return claims, nil
}

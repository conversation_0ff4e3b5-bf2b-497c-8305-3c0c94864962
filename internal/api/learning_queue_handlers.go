package api

import (
	"net/http"
	"strconv"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
)

// LearningQueueHandler handles learning queue related HTTP requests
type LearningQueueHandler struct {
	learningQueueRepo repository.ILearningQueueRepository
}

// NewLearningQueueHandler creates a new LearningQueueHandler
func NewLearningQueueHandler(learningQueueRepo repository.ILearningQueueRepository) *LearningQueueHandler {
	return &LearningQueueHandler{
		learningQueueRepo: learningQueueRepo,
	}
}

// LearningQueueRoutes creates a router for learning queue endpoints
func LearningQueueRoutes(learningQueueRepo repository.ILearningQueueRepository) http.Handler {
	h := NewLearningQueueHandler(learningQueueRepo)
	r := chi.NewRouter()

	r.Get("/due", h.GetDuePuzzles)
	r.Get("/stats", h.GetQueueStats)

	return r
}

// GetDuePuzzlesResponse defines the response for getting due puzzles
type GetDuePuzzlesResponse struct {
	Puzzles  []models.LearningQueueItem `json:"puzzles"`
	TotalDue int64                      `json:"total_due"`
}

// GetDuePuzzles handles GET /api/learning-queue/due
func (h *LearningQueueHandler) GetDuePuzzles(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Parse query parameters
	limitStr := r.URL.Query().Get("limit")
	limit := 10 // Default limit
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			if parsedLimit > 50 {
				parsedLimit = 50 // Max limit
			}
			limit = parsedLimit
		}
	}

	attemptType := r.URL.Query().Get("attempt_type")
	var attemptTypePtr *string
	if attemptType != "" {
		if attemptType != "regular" && attemptType != "arrow_duel" {
			apiError(w, r, http.StatusBadRequest, nil, "attempt_type must be 'regular' or 'arrow_duel'")
			return
		}
		attemptTypePtr = &attemptType
	}

	// Get due puzzles
	puzzles, err := h.learningQueueRepo.GetDuePuzzles(r.Context(), userID, attemptTypePtr, limit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get due puzzles")
		return
	}

	// Get total due count for response
	stats, err := h.learningQueueRepo.GetQueueStats(r.Context(), userID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get queue stats")
		return
	}

	response := GetDuePuzzlesResponse{
		Puzzles:  puzzles,
		TotalDue: stats.DueToday,
	}

	render.JSON(w, r, response)
}

// GetQueueStats handles GET /api/learning-queue/stats
func (h *LearningQueueHandler) GetQueueStats(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Get queue statistics
	stats, err := h.learningQueueRepo.GetQueueStats(r.Context(), userID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get learning queue stats")
		return
	}

	render.JSON(w, r, stats)
}

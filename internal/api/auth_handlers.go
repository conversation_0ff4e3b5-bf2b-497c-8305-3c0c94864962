package api

import (
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AuthHandler struct {
	userRepo            repository.IUserRepository
	invitationCodeRepo  repository.IInvitationCodeRepository
	authService         *service.AuthService
	eventService        *service.EventService
	firebaseAuthService *service.FirebaseAuthService
}

func NewAuthHandler(userRepo repository.IUserRepository, invitationCodeRepo repository.IInvitationCodeRepository, authService *service.AuthService, eventService *service.EventService, firebaseAuthService *service.FirebaseAuthService) *AuthHandler {
	return &AuthHandler{
		userRepo:            userRepo,
		invitationCodeRepo:  invitationCodeRepo,
		authService:         authService,
		eventService:        eventService,
		firebaseAuthService: firebaseAuthService,
	}
}

// AuthRoutes creates a router for auth endpoints
func AuthRoutes(userRepo repository.IUserRepository, invitationCodeRepo repository.IInvitationCodeRepository, authService *service.AuthService, eventService *service.EventService, firebaseAuthService *service.FirebaseAuthService, cfg *config.Config) http.Handler {
	h := NewAuthHandler(userRepo, invitationCodeRepo, authService, eventService, firebaseAuthService)
	r := chi.NewRouter()

	r.Post("/login", h.Login)
	r.Post("/register", h.Register)
	r.Post("/register-with-invitation", h.RegisterWithInvitation)
	r.Post("/firebase-exchange", h.FirebaseTokenExchange)

	// Session token management routes (authenticated)
	r.Group(func(r chi.Router) {
		r.Use(middleware.JWTAuth(cfg.JWT))
		r.Get("/session-tokens", h.ListSessionTokens)
		r.Delete("/session-tokens/{tokenID}", h.RevokeSessionToken)
	})

	// Admin routes
	r.Group(func(r chi.Router) {
		r.Use(middleware.JWTAdminOnly(cfg.JWT))
		r.Post("/admin/register", h.Register) // Keep admin registration for backward compatibility
		r.Post("/invitation-codes", h.CreateInvitationCode)
		r.Get("/invitation-codes", h.ListInvitationCodes)
	})

	return r
}

type LoginRequest struct {
	Email        string `json:"email"`
	Password     string `json:"password"`
	SessionToken string `json:"session_token,omitempty"`
}

type RegisterRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type FirebaseTokenExchangeRequest struct {
	FirebaseToken string `json:"firebase_token"`
}

// toCreateUserParams converts RegisterRequest to createUserParams
func (r RegisterRequest) toCreateUserParams() createUserParams {
	return createUserParams(r)
}

type RegisterWithInvitationRequest struct {
	Email          string `json:"email"`
	Password       string `json:"password"`
	InvitationCode string `json:"invitation_code"`
}

// toCreateUserParams converts RegisterWithInvitationRequest to createUserParams
func (r RegisterWithInvitationRequest) toCreateUserParams() createUserParams {
	return createUserParams{
		Email:    r.Email,
		Password: r.Password,
	}
}

type CreateInvitationCodeRequest struct {
	ExpiresInHours *int `json:"expires_in_hours,omitempty"` // Optional: if provided, sets an expiration time
}

type InvitationCodeResponse struct {
	ID        string     `json:"id"`
	Code      string     `json:"code"`
	CreatedAt time.Time  `json:"created_at"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
	Used      bool       `json:"used"`
}

type AuthResponse struct {
	Token        string `json:"token"`
	SessionToken string `json:"session_token,omitempty"`
}

type RegisterResponse struct {
	ID           string `json:"id"`
	Email        string `json:"email"`
	Token        string `json:"token"`
	SessionToken string `json:"session_token,omitempty"`
}

// Login handles user authentication and provides a JWT token
func (h *AuthHandler) Login(w http.ResponseWriter, r *http.Request) {
	var req LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	var user *models.User
	var err error
	userAgent := r.Header.Get("User-Agent")

	// Check if this is session token authentication
	if req.SessionToken != "" {
		// Validate session token
		sessionToken, err := h.authService.ValidateSessionToken(r.Context(), req.SessionToken)
		if err != nil {
			// Create failure event for session token authentication
			// We don't have a user ID for failed session token validation, so we'll skip the event
			if errors.Is(err, gorm.ErrRecordNotFound) {
				apiError(w, r, http.StatusUnauthorized, nil, "Invalid session token")
			} else {
				apiError(w, r, http.StatusInternalServerError, err, "Failed to validate session token")
			}
			return
		}

		// Extend session token expiration
		_, err = h.authService.ExtendSessionToken(r.Context(), req.SessionToken)
		if err != nil {
			// Log the error but continue - it's not critical for login success
			logger.FromContext(r.Context()).Error().Err(err).Str("session_token", req.SessionToken).Msg("Failed to extend session token")
		}

		user = &sessionToken.User

		// Create success event for session token authentication
		if err := h.eventService.CreateSignInSuccessEvent(r.Context(), user.ID, "session_token", userAgent); err != nil {
			// Log the error but continue - it's not critical for login success
			logger.FromContext(r.Context()).Error().Err(err).Str("user_id", user.ID).Msg("Failed to create sign-in success event")
		}
	} else {
		// Validate email/password request
		if req.Email == "" || req.Password == "" {
			apiError(w, r, http.StatusBadRequest, nil, "Email and password are required, or provide session_token")
			return
		}

		// Get user from database
		user, err = h.userRepo.GetByEmail(r.Context(), req.Email)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// We don't have a user ID for failed email lookup, so we'll skip the event
				apiError(w, r, http.StatusUnauthorized, nil, "Invalid credentials")
			} else {
				// Log the actual error, return a generic message
				apiError(w, r, http.StatusInternalServerError, err, "Database error while retrieving user")
			}
			return
		}

		// Verify password
		if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
			// Create failure event for password authentication
			if err := h.eventService.CreateSignInFailureEvent(r.Context(), user.ID, "password", userAgent); err != nil {
				// Log the error but continue
				logger.FromContext(r.Context()).Error().Err(err).Str("user_id", user.ID).Msg("Failed to create sign-in failure event")
			}
			apiError(w, r, http.StatusUnauthorized, nil, "Invalid credentials")
			return
		}

		// Create success event for password authentication
		if err := h.eventService.CreateSignInSuccessEvent(r.Context(), user.ID, "password", userAgent); err != nil {
			// Log the error but continue - it's not critical for login success
			logger.FromContext(r.Context()).Error().Err(err).Str("user_id", user.ID).Msg("Failed to create sign-in success event")
		}
	}

	// Generate JWT token (regular user, not admin)
	token, err := h.authService.GenerateToken(user.ID, user.Email, false)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to generate token")
		return
	}

	// Generate session token if this is email/password login
	var sessionTokenString string
	if req.SessionToken == "" {
		sessionToken, err := h.authService.GenerateSessionToken(r.Context(), user.ID, userAgent)
		if err != nil {
			// Log the error but continue - it's not critical for login success
			logger.FromContext(r.Context()).Error().Err(err).Str("user_id", user.ID).Msg("Failed to generate session token")
		} else if sessionToken != nil {
			sessionTokenString = sessionToken.Token
		}
	}

	// Update last sign in time
	if err := h.userRepo.UpdateLastSignIn(r.Context(), user.ID); err != nil {
		// Log the error but continue - it's not critical for login success
		logger.FromContext(r.Context()).Error().Err(err).Str("user_id", user.ID).Msg("Failed to update last sign in time")
	}

	render.JSON(w, r, AuthResponse{
		Token:        token,
		SessionToken: sessionTokenString,
	})
}

func (h *AuthHandler) Register(w http.ResponseWriter, r *http.Request) {
	var req RegisterRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Create user using common logic
	params := req.toCreateUserParams()
	userAgent := r.Header.Get("User-Agent")
	result := h.createUser(r.Context(), params, userAgent)

	// Handle error if any
	if result.Error != nil {
		apiError(w, r, result.Error.Status, result.Error.Err, result.Error.Message)
		return
	}

	// Indicate 201 status
	render.Status(r, http.StatusCreated)

	// Return register response with user info and tokens for backward compatibility
	render.JSON(w, r, RegisterResponse{
		ID:           result.User.ID,
		Email:        result.User.Email,
		Token:        result.Token,
		SessionToken: result.SessionToken,
	})
}

type AdminTokenRequest struct {
	UserID string `json:"user_id"`
	Email  string `json:"email"`
}

// GenerateAdminToken creates an admin token for testing purposes
// In a production environment, this would require strong authentication
func (h *AuthHandler) GenerateAdminToken(w http.ResponseWriter, r *http.Request) {
	var req AdminTokenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.UserID == "" || req.Email == "" {
		apiError(w, r, http.StatusBadRequest, nil, "user_id and email are required")
		return
	}

	// Generate admin token
	token, err := h.authService.GenerateToken(req.UserID, req.Email, true)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to generate token")
		return
	}

	render.JSON(w, r, AuthResponse{Token: token})
}

// ListSessionTokens lists all session tokens for the current user
func (h *AuthHandler) ListSessionTokens(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		apiError(w, r, http.StatusUnauthorized, nil, "User ID not found in context")
		return
	}

	tokens, err := h.authService.ListUserSessionTokens(r.Context(), userID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to list session tokens")
		return
	}

	// Convert to response format
	response := make([]models.SessionTokenResponse, len(tokens))
	for i, token := range tokens {
		response[i] = token.ToResponse()
	}

	render.JSON(w, r, response)
}

// RevokeSessionToken revokes a specific session token
func (h *AuthHandler) RevokeSessionToken(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		apiError(w, r, http.StatusUnauthorized, nil, "User ID not found in context")
		return
	}

	tokenID := chi.URLParam(r, "tokenID")
	if tokenID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Token ID is required")
		return
	}

	// Verify the token belongs to the user
	sessionToken, err := h.authService.GetSessionTokenByID(r.Context(), tokenID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Session token not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to get session token")
		}
		return
	}

	if sessionToken.UserID != userID {
		apiError(w, r, http.StatusForbidden, nil, "Cannot revoke another user's session token")
		return
	}

	// Revoke the token
	err = h.authService.RevokeSessionToken(r.Context(), tokenID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to revoke session token")
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// FirebaseTokenExchange exchanges a Firebase ID token for our JWT token
func (h *AuthHandler) FirebaseTokenExchange(w http.ResponseWriter, r *http.Request) {
	var req FirebaseTokenExchangeRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	if req.FirebaseToken == "" {
		apiError(w, r, http.StatusBadRequest, nil, "firebase_token is required")
		return
	}

	// Verify Firebase token
	claims, err := h.firebaseAuthService.VerifyIDToken(r.Context(), req.FirebaseToken)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Invalid Firebase token")
		return
	}

	// Get or create user based on Firebase UID
	user, isNewUser, err := h.userRepo.GetOrCreateFirebaseUser(r.Context(), claims.UserID, claims.Email)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get or create user")
		return
	}

	// Generate JWT token (regular user, not admin)
	token, err := h.authService.GenerateToken(user.ID, user.Email, false)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to generate token")
		return
	}

	// Note: We don't generate session tokens for Firebase authentication
	// Firebase provides its own refresh tokens for token renewal

	// Create event for Firebase authentication
	userAgent := r.Header.Get("User-Agent")
	signInType := claims.Firebase.SignInProvider // Get the actual provider from Firebase token (e.g., "password", "google.com", "github.com")
	if isNewUser {
		// Create registration event for new users
		if err := h.eventService.CreateRegistrationEvent(r.Context(), user.ID, signInType, userAgent); err != nil {
			logger.FromContext(r.Context()).Error().Err(err).Str("user_id", user.ID).Msg("Failed to create registration event")
		}
	} else {
		// Create sign-in event for existing users
		if err := h.eventService.CreateSignInSuccessEvent(r.Context(), user.ID, signInType, userAgent); err != nil {
			logger.FromContext(r.Context()).Error().Err(err).Str("user_id", user.ID).Msg("Failed to create sign-in success event")
		}
	}

	// Update last sign in time
	if err := h.userRepo.UpdateLastSignIn(r.Context(), user.ID); err != nil {
		logger.FromContext(r.Context()).Error().Err(err).Str("user_id", user.ID).Msg("Failed to update last sign in time")
	}

	// Return response with user info and JWT token (no session token for Firebase auth)
	response := struct {
		Token string      `json:"token"`
		User  interface{} `json:"user"`
	}{
		Token: token,
		User: struct {
			ID          string  `json:"id"`
			Email       string  `json:"email"`
			FirebaseUID *string `json:"firebase_uid,omitempty"`
		}{
			ID:          user.ID,
			Email:       user.Email,
			FirebaseUID: user.FirebaseUID,
		},
	}

	if isNewUser {
		render.Status(r, http.StatusCreated)
	}

	render.JSON(w, r, response)
}

package api

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/chessticize/chessticize-server/internal/verification"
	"github.com/go-chi/chi/v5"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupPuzzleAttemptTest(t *testing.T) (repository.IUserRepository, repository.IGameRepository, repository.IPuzzleRepository, repository.IEventRepository, http.Handler) {
	// Create a fake DB for testing
	db := fake.NewDB(t)

	// Create repositories
	userRepo := fake.NewUserRepository(db)
	gameRepo := fake.NewGameRepository(db)
	puzzleRepo := fake.NewPuzzleRepository(db)
	taskRepo := fake.NewFakeTaskRepository(db)
	puzzleQueueRepo := fake.NewFakePuzzleQueueRepository(db)
	eventRepo := fake.NewEventRepository(db.DB)
	userDailyStatsRepo := fake.NewUserDailyStatsRepository(db.DB)

	// Create services
	jwtConfig := testutils.GetTestJWTConfig()
	sessionTokenConfig := config.SessionTokenConfig{ExpiryDays: 30}
	eventService := service.NewEventService(eventRepo)
	chessVerifier := verification.NewChessProfileVerifier()

	// Create test config
	testConfig := &config.Config{
		JWT:          jwtConfig,
		SessionToken: sessionTokenConfig,
	}

	// Create the router
	r := chi.NewRouter()
	r.Route("/api/v1", func(r chi.Router) {
		r.Route("/users/me", func(r chi.Router) {
			r.Use(middleware.JWTAuth(testConfig.JWT))
			userEloRepo := repository.NewUserEloRepository(testutils.GetFakeDB(t).DB)
			userSprintDailyStatsRepo := repository.NewUserSprintDailyStatsRepository(testutils.GetFakeDB(t).DB)
			userLearningDailyStatsRepo := fake.NewFakeUserLearningDailyStatsRepository(testutils.GetFakeDB(t))
			learningQueueRepo := fake.NewFakeLearningQueueRepository(testutils.GetFakeDB(t), userLearningDailyStatsRepo)
			hUser := NewUserHandler(userRepo, gameRepo, puzzleRepo, taskRepo, puzzleQueueRepo, learningQueueRepo, userDailyStatsRepo, userEloRepo, userSprintDailyStatsRepo, chessVerifier, eventService)
			r.Post("/puzzles/{puzzleID}/attempts", hUser.PostPuzzleAttempt)
		})
	})

	return userRepo, gameRepo, puzzleRepo, eventRepo, r
}

func TestPostPuzzleAttempt(t *testing.T) {
	userRepo, gameRepo, puzzleRepo, eventRepo, router := setupPuzzleAttemptTest(t)

	// Create a test user
	testUser := testutils.CreateTestUser(t, userRepo)

	// Create a test game first (required for puzzle)
	pgn := "1. e4 e5 2. Nf3 Nc6"
	compressedPGN, err := utils.CompressPGN(pgn)
	require.NoError(t, err)

	url := "https://chess.com/game/123"
	testGame := &models.Game{
		UserID:        testUser.ID,
		Platform:      models.ChessDotCom,
		ChessUsername: "testuser",
		UserColor:     models.White,
		CompressedPGN: compressedPGN,
		TimeControl:   "5+0",
		Rated:         true,
		URL:           &url,
		WhitePlayer:   "testuser",
		BlackPlayer:   "opponent",
		Winner:        models.WinnerWhite,
		Result:        models.Mate,
	}
	err = gameRepo.Create(context.TODO(), testGame)
	require.NoError(t, err)

	// Create a test puzzle
	testPuzzle := &models.Puzzle{
		GameID:      testGame.ID,
		UserID:      testUser.ID,
		GameMove:    15,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       pq.StringArray{"e2e4", "e7e5"},
		PrevCP:      -50,
		CP:          150,
		Theme:       models.OpponentMistakeCaught,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    false,
		Tags:        pq.StringArray{"tactics", "test"},
	}
	err = puzzleRepo.Create(context.TODO(), testPuzzle)
	require.NoError(t, err)

	t.Run("PostPuzzleAttempt_Success_Solved", func(t *testing.T) {
		// Create puzzle attempt request
		attemptReq := PostPuzzleAttemptRequest{
			Moves:     []string{"Qh5", "g6", "Qxf7#"},
			Solved:    true,
			TimeSpent: 45,
		}

		// Generate token for the test user
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzles/"+testPuzzle.ID+"/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Verify event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), testUser.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypePuzzle},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, models.EventTypePuzzle, event.EventType)

		// Parse event data
		var eventData models.PuzzleEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)

		assert.Equal(t, testPuzzle.ID, eventData.PuzzleID)
		assert.Equal(t, models.PuzzleTypeUser, eventData.PuzzleType)
		assert.True(t, eventData.Solved)
		assert.Equal(t, 45, eventData.TimeSpent)
		assert.Equal(t, []string{"Qh5", "g6", "Qxf7#"}, eventData.MovesPlayed) // Actual moves in the request
	})

	t.Run("PostPuzzleAttempt_Success_Failed", func(t *testing.T) {
		// Create another test user to avoid event conflicts
		testUser2 := testutils.CreateTestUser(t, userRepo)

		// Create a puzzle for the second user
		testPuzzle2 := &models.Puzzle{
			GameID:      testGame.ID,
			UserID:      testUser2.ID,
			GameMove:    20,
			FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:       pq.StringArray{"d2d4", "d7d5"},
			PrevCP:      10,
			CP:          -100,
			Theme:       models.OpponentBlunderMissed,
			UserColor:   models.Black,
			PuzzleColor: models.White,
			Zugzwang:    false,
			Tags:        pq.StringArray{"blunder", "test"},
		}
		err = puzzleRepo.Create(context.TODO(), testPuzzle2)
		require.NoError(t, err)

		// Create puzzle attempt request (failed attempt)
		attemptReq := PostPuzzleAttemptRequest{
			Moves:     []string{"Qh4", "g6"},
			Solved:    false,
			TimeSpent: 120,
		}

		// Generate token for the second test user
		token := testutils.GenerateUserToken(t, testUser2.ID, testUser2.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzles/"+testPuzzle2.ID+"/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Verify event was created with correct data
		events, _, err := eventRepo.ListByUserID(context.TODO(), testUser2.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypePuzzle},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		// Parse event data
		var eventData models.PuzzleEventData
		err = json.Unmarshal(events[0].EventData, &eventData)
		require.NoError(t, err)

		assert.Equal(t, testPuzzle2.ID, eventData.PuzzleID)
		assert.Equal(t, models.PuzzleTypeUser, eventData.PuzzleType)
		assert.False(t, eventData.Solved)
		assert.Equal(t, 120, eventData.TimeSpent)
		assert.Equal(t, []string{"Qh4", "g6"}, eventData.MovesPlayed)
	})

	t.Run("PostPuzzleAttempt_PuzzleNotFound", func(t *testing.T) {
		// Create puzzle attempt request for non-existent puzzle
		attemptReq := PostPuzzleAttemptRequest{
			Moves:     []string{"e2e4"},
			Solved:    true,
			TimeSpent: 30,
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzles/non-existent-id/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusNotFound, resp.Code)
	})

	t.Run("PostPuzzleAttempt_PuzzleNotOwnedByUser", func(t *testing.T) {
		// Create another user
		otherUser := testutils.CreateTestUser(t, userRepo)

		// Try to access the first user's puzzle with the second user's token
		attemptReq := PostPuzzleAttemptRequest{
			Moves:     []string{"e2e4"},
			Solved:    true,
			TimeSpent: 30,
		}

		token := testutils.GenerateUserToken(t, otherUser.ID, otherUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzles/"+testPuzzle.ID+"/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusForbidden, resp.Code)
	})

	t.Run("PostPuzzleAttempt_InvalidTimeSpent", func(t *testing.T) {
		// Create puzzle attempt request with negative time
		attemptReq := PostPuzzleAttemptRequest{
			Moves:     []string{"e2e4"},
			Solved:    true,
			TimeSpent: -10,
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzles/"+testPuzzle.ID+"/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("PostPuzzleAttempt_MissingPuzzleID", func(t *testing.T) {
		// Create puzzle attempt request
		attemptReq := PostPuzzleAttemptRequest{
			Moves:     []string{"e2e4"},
			Solved:    true,
			TimeSpent: 30,
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		// Use empty puzzle ID in URL
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzles//attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response - this should result in a 400 due to missing puzzle ID
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("PostPuzzleAttempt_Unauthorized", func(t *testing.T) {
		// Create puzzle attempt request without token
		attemptReq := PostPuzzleAttemptRequest{
			Moves:     []string{"e2e4"},
			Solved:    true,
			TimeSpent: 30,
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzles/"+testPuzzle.ID+"/attempts", attemptReq)
		// Don't set Authorization header
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})

	t.Run("PostPuzzleAttempt_EmptyMoves", func(t *testing.T) {
		// Create puzzle attempt request with empty moves (should still work)
		attemptReq := PostPuzzleAttemptRequest{
			Moves:     []string{},
			Solved:    false,
			TimeSpent: 5,
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzles/"+testPuzzle.ID+"/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Verify event was created with 0 moves
		events, _, err := eventRepo.ListByUserID(context.TODO(), testUser.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypePuzzle},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 2) // Should have 2 events now (from previous test + this one)

		// Find the latest event
		var latestEvent *models.Event
		for i := range events {
			if latestEvent == nil || events[i].CreatedAt.After(latestEvent.CreatedAt) {
				latestEvent = &events[i]
			}
		}
		require.NotNil(t, latestEvent)

		// Parse event data
		var eventData models.PuzzleEventData
		err = json.Unmarshal(latestEvent.EventData, &eventData)
		require.NoError(t, err)

		assert.Equal(t, []string{}, eventData.MovesPlayed)
		assert.False(t, eventData.Solved)
		assert.Equal(t, 5, eventData.TimeSpent)
		assert.Equal(t, models.PuzzleTypeUser, eventData.PuzzleType)
	})
}

package api

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"gorm.io/gorm"
)

type SprintHandler struct {
	sprintService service.ISprintService
	puzzleService service.IPuzzleService
}

func NewSprintHandler(sprintService service.ISprintService, puzzleService service.IPuzzleService) *SprintHandler {
	return &SprintHandler{
		sprintService: sprintService,
		puzzleService: puzzleService,
	}
}

// SprintRoutes creates a router for sprint endpoints
func SprintRoutes(sprintService service.ISprintService, puzzleService service.IPuzzleService) http.Handler {
	h := NewSprintHandler(sprintService, puzzleService)
	r := chi.NewRouter()

	r.<PERSON>("/start", h.StartSprint)
	r.Get("/{sessionId}", h.GetSprintState)
	r.Post("/{sessionId}/end", h.EndSprint)
	r.Post("/{sessionId}/next-puzzles", h.GetNextPuzzles)
	r.Post("/{sessionId}/results", h.SubmitPuzzleResults)
	r.Get("/{sessionId}/puzzles", h.GetSprintPuzzles)

	return r
}

// API Request/Response Models

type StartSprintRequest struct {
	EloType string `json:"elo_type"`
}

type StartSprintResponse struct {
	SessionID        string  `json:"session_id"`
	UserElo          UserElo `json:"user_elo"`
	TargetPuzzles    int     `json:"target_puzzles"`
	TimeLimitSeconds int     `json:"time_limit_seconds"`
	MaxMistakes      int     `json:"max_mistakes"`
	AttemptType      string  `json:"attempt_type,omitempty"` // NEW: "regular" or "arrow_duel"
}

type UserElo struct {
	Rating          int     `json:"rating"`
	RatingDeviation float64 `json:"rating_deviation"`
	IsProvisional   bool    `json:"is_provisional"`
}

type SprintStateResponse struct {
	SessionID            string              `json:"session_id"`
	Status               models.SprintStatus `json:"status"`
	PuzzlesSolved        int                 `json:"puzzles_solved"`
	MistakesMade         int                 `json:"mistakes_made"`
	MaxMistakes          int                 `json:"max_mistakes"`
	TargetPuzzles        int                 `json:"target_puzzles"`
	StartedAt            time.Time           `json:"started_at"`
	TimeRemainingSeconds *int                `json:"time_remaining_seconds,omitempty"`
}

type NextPuzzlesRequest struct {
	Count int `json:"count,omitempty"` // Optional, defaults to 10
}

type NextPuzzlesResponse struct {
	Puzzles []PuzzleForSprint `json:"puzzles"`
}

type PuzzleForSprint struct {
	PuzzleID         string   `json:"puzzle_id"`
	FEN              string   `json:"fen"`
	SolutionMoves    []string `json:"solution_moves"`
	Rating           int      `json:"rating"`
	Themes           []string `json:"themes"`
	SequenceInSprint int      `json:"sequence_in_sprint"`
	AttemptType      string   `json:"attempt_type,omitempty"` // "regular" or "arrow_duel"

	// Arrow-duel specific fields (only populated for arrow-duel sprints)
	BestMoveEval               *float64 `json:"best_move_eval,omitempty"`
	BestMove                   string   `json:"best_move,omitempty"`
	PositionEvalAfterFirstMove *float64 `json:"position_eval_after_first_move,omitempty"`
}

type SubmitResultsRequest struct {
	Results []PuzzleAttemptRequest `json:"results"`
}

type PuzzleAttemptRequest struct {
	PuzzleID         string    `json:"puzzle_id"`
	SequenceInSprint int       `json:"sequence_in_sprint"`
	UserMoves        []string  `json:"user_moves"`
	WasCorrect       bool      `json:"was_correct"`
	TimeTakenMs      int       `json:"time_taken_ms"`
	AttemptedAt      time.Time `json:"attempted_at"`

	// Arrow-duel specific fields (only used when AttemptType is "arrow_duel")
	AttemptType    string   `json:"attempt_type,omitempty"`    // NEW: "regular" or "arrow_duel"
	CandidateMoves []string `json:"candidate_moves,omitempty"` // NEW: [blunder_move, correct_move]
	ChosenMove     *string  `json:"chosen_move,omitempty"`     // NEW: Move chosen by player
}

type SubmitResultsResponse struct {
	ProcessedCount int                 `json:"processed_count"`
	SessionStatus  models.SprintStatus `json:"session_status"`
	MistakesCount  int                 `json:"mistakes_count"`
}

type EndSprintRequest struct {
	// Optional client-provided final results to override server calculations
	PuzzlesSolved *int `json:"puzzles_solved,omitempty"` // Client's count of puzzles solved
	MistakesMade  *int `json:"mistakes_made,omitempty"`  // Client's count of mistakes made
}

// Bind implements render.Binder interface for EndSprintRequest
func (req *EndSprintRequest) Bind(r *http.Request) error {
	// Validate client-provided values if present
	if req.PuzzlesSolved != nil && *req.PuzzlesSolved < 0 {
		return fmt.Errorf("puzzles_solved cannot be negative")
	}
	if req.MistakesMade != nil && *req.MistakesMade < 0 {
		return fmt.Errorf("mistakes_made cannot be negative")
	}
	return nil
}

type EndSprintResponse struct {
	SessionID       string              `json:"session_id"`
	Status          models.SprintStatus `json:"status"`
	PuzzlesSolved   int                 `json:"puzzles_solved"`
	MistakesMade    int                 `json:"mistakes_made"`
	MaxMistakes     int                 `json:"max_mistakes"`
	TargetPuzzles   int                 `json:"target_puzzles"`
	DurationSeconds int                 `json:"duration_seconds"`
	EloChange       *EloChangeResponse  `json:"elo_change,omitempty"`
}

type EloChangeResponse struct {
	RatingBefore int `json:"rating_before"`
	RatingAfter  int `json:"rating_after"`
	RatingChange int `json:"rating_change"`
}

type SprintPuzzlesResponse struct {
	Puzzles    []SprintPuzzleResponse `json:"puzzles"`
	TotalCount int64                  `json:"total_count"`
	Offset     int                    `json:"offset"`
	Limit      int                    `json:"limit"`
}

type SprintPuzzleResponse struct {
	PuzzleID         string   `json:"puzzle_id"`
	SequenceInSprint int      `json:"sequence_in_sprint"`
	FEN              string   `json:"fen"`
	SolutionMoves    []string `json:"solution_moves"`
	Rating           int      `json:"rating"`
	Themes           []string `json:"themes"`
	AttemptStatus    string   `json:"attempt_status"`
	UserMoves        []string `json:"user_moves,omitempty"`
	WasCorrect       *bool    `json:"was_correct,omitempty"`
	TimeTakenMs      *int     `json:"time_taken_ms,omitempty"`
	AttemptedAt      *string  `json:"attempted_at,omitempty"`

	// Arrow-duel specific fields
	AttemptType    *string  `json:"attempt_type,omitempty"`
	CandidateMoves []string `json:"candidate_moves,omitempty"`
	ChosenMove     *string  `json:"chosen_move,omitempty"`
}

// Handler Methods

// StartSprint handles POST /api/v1/sprint/start
func (h *SprintHandler) StartSprint(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	var req StartSprintRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.EloType == "" {
		apiError(w, r, http.StatusBadRequest, nil, "elo_type is required")
		return
	}

	// Start the sprint
	sprint, err := h.sprintService.StartSprint(r.Context(), userID, req.EloType)
	if err != nil {
		// Check if this is a validation error (ELO type validation)
		if strings.Contains(err.Error(), "invalid ELO type:") {
			apiError(w, r, http.StatusBadRequest, err, "Invalid ELO type")
			return
		}
		apiError(w, r, http.StatusInternalServerError, err, "Failed to start sprint")
		return
	}

	// Determine attempt type from ELO type
	attemptType := "regular"
	if strings.HasPrefix(req.EloType, "arrowduel ") {
		attemptType = "arrow_duel"
	}

	// Prepare response
	resp := StartSprintResponse{
		SessionID: sprint.ID,
		UserElo: UserElo{
			Rating:          sprint.EloRatingBefore,
			RatingDeviation: 200.0, // Default for now, could be fetched from user ELO
			IsProvisional:   false, // Could be determined based on games played
		},
		TargetPuzzles:    sprint.TargetPuzzles,
		TimeLimitSeconds: sprint.TimeLimitSeconds,
		MaxMistakes:      sprint.MaxMistakes,
		AttemptType:      attemptType,
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, resp)
}

// GetSprintState handles GET /api/v1/sprint/{sessionId}
func (h *SprintHandler) GetSprintState(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	sessionID := chi.URLParam(r, "sessionId")
	if sessionID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Session ID is required")
		return
	}

	// Get sprint state
	sprint, err := h.sprintService.GetSprintState(r.Context(), sessionID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Sprint session not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to get sprint state")
		}
		return
	}

	// Calculate time remaining
	var timeRemaining *int
	if sprint.Status == models.SprintStatusActive {
		elapsed := int(time.Since(sprint.StartedAt).Seconds())
		remaining := sprint.TimeLimitSeconds - elapsed
		if remaining > 0 {
			timeRemaining = &remaining
		} else {
			zero := 0
			timeRemaining = &zero
		}
	}

	resp := SprintStateResponse{
		SessionID:            sprint.ID,
		Status:               sprint.Status,
		PuzzlesSolved:        sprint.PuzzlesSolved,
		MistakesMade:         sprint.MistakesMade,
		MaxMistakes:          sprint.MaxMistakes,
		TargetPuzzles:        sprint.TargetPuzzles,
		StartedAt:            sprint.StartedAt,
		TimeRemainingSeconds: timeRemaining,
	}

	render.JSON(w, r, resp)
}

// EndSprint handles POST /api/v1/sprint/{sessionId}/end
func (h *SprintHandler) EndSprint(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	sessionID := chi.URLParam(r, "sessionId")
	if sessionID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Session ID is required")
		return
	}

	// Parse optional request body for client-provided final results
	var req EndSprintRequest
	if r.ContentLength > 0 {
		if err := render.Bind(r, &req); err != nil {
			apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
			return
		}
	}

	// End the sprint with optional client-provided results
	sprint, err := h.sprintService.EndSprintWithClientResults(r.Context(), sessionID, userID, req.PuzzlesSolved, req.MistakesMade)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Sprint session not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to end sprint")
		}
		return
	}

	// Prepare response
	resp := EndSprintResponse{
		SessionID:       sprint.ID,
		Status:          sprint.Status,
		PuzzlesSolved:   sprint.PuzzlesSolved,
		MistakesMade:    sprint.MistakesMade,
		MaxMistakes:     sprint.MaxMistakes,
		TargetPuzzles:   sprint.TargetPuzzles,
		DurationSeconds: *sprint.DurationSeconds,
	}

	// Add ELO change if available
	if sprint.EloChange != nil {
		resp.EloChange = &EloChangeResponse{
			RatingBefore: sprint.EloRatingBefore,
			RatingAfter:  *sprint.EloRatingAfter,
			RatingChange: *sprint.EloChange,
		}
	}

	render.JSON(w, r, resp)
}

// GetNextPuzzles handles POST /api/v1/sprint/{sessionId}/next-puzzles
func (h *SprintHandler) GetNextPuzzles(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	sessionID := chi.URLParam(r, "sessionId")
	if sessionID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Session ID is required")
		return
	}

	// Parse request body
	var req NextPuzzlesRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		// If no body provided, use defaults
		req.Count = 10
	}

	// Validate count parameter
	count := req.Count
	if count <= 0 {
		count = 10 // default
	}
	if count > 50 {
		apiError(w, r, http.StatusBadRequest, nil, "Count parameter must be <= 50")
		return
	}

	// Get sprint to determine attempt type
	sprint, err := h.sprintService.GetSprintState(r.Context(), sessionID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Sprint session not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to get sprint")
		}
		return
	}

	// Determine attempt type from ELO type
	attemptType := "regular"
	if strings.HasPrefix(sprint.EloType, "arrowduel ") {
		attemptType = "arrow_duel"
	}

	// Get next puzzles for sprint
	puzzles, err := h.sprintService.GetNextPuzzles(r.Context(), sessionID, userID, count)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Sprint session not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to get next puzzles for sprint")
		}
		return
	}

	// Get the total count of sprint puzzles to calculate sequence numbers
	// Since the service just created the new puzzles, we can get the total count
	totalCount, err := h.sprintService.GetSprintPuzzleCount(r.Context(), sessionID, userID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get sprint puzzle count")
		return
	}

	// Calculate the starting sequence number for the returned puzzles
	// The new puzzles start from (totalCount - len(puzzles) + 1)
	startingSequence := totalCount - len(puzzles) + 1

	// Convert to response format
	puzzleResponses := make([]PuzzleForSprint, len(puzzles))
	for i, puzzle := range puzzles {
		// Convert pq.StringArray to []string for solution moves
		solutionMoves := make([]string, len(puzzle.Moves))
		copy(solutionMoves, puzzle.Moves)

		// Convert pq.StringArray to []string for themes
		themes := make([]string, len(puzzle.Themes))
		copy(themes, puzzle.Themes)

		puzzleResponse := PuzzleForSprint{
			PuzzleID:         puzzle.ID,
			FEN:              puzzle.FEN,
			SolutionMoves:    solutionMoves,
			Rating:           puzzle.Rating,
			Themes:           themes,
			SequenceInSprint: startingSequence + i,
			AttemptType:      attemptType,
		}

		// Include evaluation data for arrow-duel sprints
		if attemptType == "arrow_duel" {
			puzzleResponse.BestMoveEval = puzzle.BestMoveEval
			puzzleResponse.BestMove = puzzle.BestMove
			puzzleResponse.PositionEvalAfterFirstMove = puzzle.PositionEvalAfterFirstMove
		}

		puzzleResponses[i] = puzzleResponse
	}

	resp := NextPuzzlesResponse{
		Puzzles: puzzleResponses,
	}

	render.JSON(w, r, resp)
}

// SubmitPuzzleResults handles POST /api/v1/sprint/{sessionId}/results
func (h *SprintHandler) SubmitPuzzleResults(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	sessionID := chi.URLParam(r, "sessionId")
	if sessionID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Session ID is required")
		return
	}

	var req SubmitResultsRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if len(req.Results) == 0 {
		apiError(w, r, http.StatusBadRequest, nil, "At least one result is required")
		return
	}

	// Validate arrow-duel specific fields for each result
	for i, result := range req.Results {
		attemptType := result.AttemptType
		if attemptType == "" {
			attemptType = "regular" // Default
		}

		if attemptType == "arrow_duel" {
			// Validate candidate moves
			if len(result.CandidateMoves) != 2 {
				apiError(w, r, http.StatusBadRequest, nil, fmt.Sprintf("Result %d: Arrow-duel attempts must have exactly 2 candidate moves", i))
				return
			}

			// Validate chosen move
			if result.ChosenMove == nil {
				apiError(w, r, http.StatusBadRequest, nil, fmt.Sprintf("Result %d: Arrow-duel attempts must specify chosen_move", i))
				return
			}

			// Validate that chosen move is one of the candidate moves
			chosenMove := *result.ChosenMove
			if chosenMove != result.CandidateMoves[0] && chosenMove != result.CandidateMoves[1] {
				apiError(w, r, http.StatusBadRequest, nil, fmt.Sprintf("Result %d: chosen_move must be one of the candidate_moves", i))
				return
			}
		}
	}

	// Convert to service format
	serviceResults := make([]service.PuzzleAttemptResult, len(req.Results))
	for i, result := range req.Results {
		attemptType := result.AttemptType
		if attemptType == "" {
			attemptType = "regular" // Default
		}

		serviceResults[i] = service.PuzzleAttemptResult{
			PuzzleID:         result.PuzzleID,
			SequenceInSprint: result.SequenceInSprint,
			UserMoves:        result.UserMoves,
			WasCorrect:       result.WasCorrect,
			TimeTakenMs:      result.TimeTakenMs,
			AttemptedAt:      result.AttemptedAt,
			AttemptType:      attemptType,
			CandidateMoves:   result.CandidateMoves,
			ChosenMove:       result.ChosenMove,
		}
	}

	// Submit results
	response, err := h.puzzleService.SubmitPuzzleResults(r.Context(), sessionID, userID, serviceResults)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Sprint session not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to submit puzzle results")
		}
		return
	}

	resp := SubmitResultsResponse{
		ProcessedCount: response.ProcessedCount,
		SessionStatus:  response.SessionStatus,
		MistakesCount:  response.MistakesCount,
	}

	render.JSON(w, r, resp)
}

// GetSprintPuzzles handles GET /api/v1/sprint/{sessionId}/puzzles
func (h *SprintHandler) GetSprintPuzzles(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	sessionID := chi.URLParam(r, "sessionId")
	if sessionID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Session ID is required")
		return
	}

	// Parse query parameters
	offset := 0
	if offsetStr := r.URL.Query().Get("offset"); offsetStr != "" {
		if parsedOffset, err := strconv.Atoi(offsetStr); err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	limit := 50 // default
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 && parsedLimit <= 100 {
			limit = parsedLimit
		}
	}

	// Build filter
	var filter repository.SprintPuzzleFilter

	// Status filter
	if statusStr := r.URL.Query().Get("status"); statusStr != "" {
		switch statusStr {
		case "unattempted":
			status := repository.SprintPuzzleStatusUnattempted
			filter.Status = &status
		case "solved":
			status := repository.SprintPuzzleStatusSolved
			filter.Status = &status
		case "failed":
			status := repository.SprintPuzzleStatusFailed
			filter.Status = &status
		case "attempted":
			status := repository.SprintPuzzleStatusAttempted
			filter.Status = &status
		default:
			apiError(w, r, http.StatusBadRequest, nil, "Invalid status filter. Valid values: unattempted, solved, failed, attempted")
			return
		}
	}

	// Sequence filters
	if sequenceMinStr := r.URL.Query().Get("sequence_min"); sequenceMinStr != "" {
		if sequenceMin, err := strconv.Atoi(sequenceMinStr); err == nil && sequenceMin > 0 {
			filter.SequenceMin = &sequenceMin
		}
	}

	if sequenceMaxStr := r.URL.Query().Get("sequence_max"); sequenceMaxStr != "" {
		if sequenceMax, err := strconv.Atoi(sequenceMaxStr); err == nil && sequenceMax > 0 {
			filter.SequenceMax = &sequenceMax
		}
	}

	// Attempt type filter
	if attemptTypeStr := r.URL.Query().Get("attempt_type"); attemptTypeStr != "" {
		if attemptTypeStr == "regular" || attemptTypeStr == "arrow_duel" {
			filter.AttemptType = &attemptTypeStr
		} else {
			apiError(w, r, http.StatusBadRequest, nil, "Invalid attempt_type filter. Valid values: regular, arrow_duel")
			return
		}
	}

	// Get sprint puzzles
	sprintPuzzles, totalCount, err := h.sprintService.GetSprintPuzzles(r.Context(), sessionID, userID, filter, offset, limit)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Sprint session not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to get sprint puzzles")
		}
		return
	}

	// Convert to response format
	puzzleResponses := make([]SprintPuzzleResponse, len(sprintPuzzles))
	for i, sp := range sprintPuzzles {
		var attemptedAtStr *string
		if sp.AttemptedAt != nil {
			timeStr := sp.AttemptedAt.Format(time.RFC3339)
			attemptedAtStr = &timeStr
		}

		puzzleResponses[i] = SprintPuzzleResponse{
			PuzzleID:         sp.LichessPuzzle.ID,
			SequenceInSprint: sp.SprintPuzzle.SequenceInSprint,
			FEN:              sp.LichessPuzzle.FEN,
			SolutionMoves:    sp.LichessPuzzle.Moves,
			Rating:           sp.LichessPuzzle.Rating,
			Themes:           sp.LichessPuzzle.Themes,
			AttemptStatus:    string(sp.AttemptStatus),
			UserMoves:        sp.UserMoves,
			WasCorrect:       sp.WasCorrect,
			TimeTakenMs:      sp.TimeTakenMs,
			AttemptedAt:      attemptedAtStr,
			AttemptType:      sp.AttemptType,
			CandidateMoves:   sp.CandidateMoves,
			ChosenMove:       sp.ChosenMove,
		}
	}

	resp := SprintPuzzlesResponse{
		Puzzles:    puzzleResponses,
		TotalCount: totalCount,
		Offset:     offset,
		Limit:      limit,
	}

	render.JSON(w, r, resp)
}

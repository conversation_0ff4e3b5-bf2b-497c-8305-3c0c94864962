package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/chessticize/chessticize-server/internal/verification"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type UserHandler struct {
	userRepo                 repository.IUserRepository
	gameRepo                 repository.IGameRepository
	puzzleRepo               repository.IPuzzleRepository
	taskRepo                 repository.ITaskRepository
	puzzleQueueRepo          repository.IPuzzleQueueRepository
	learningQueueRepo        repository.ILearningQueueRepository
	userDailyStatsRepo       repository.IUserDailyStatsRepository
	userEloRepo              repository.IUserEloRepository
	userSprintDailyStatsRepo repository.IUserSprintDailyStatsRepository
	chessVerifier            verification.IChessProfileVerifier
	eventService             *service.EventService
}

func NewUserHandler(userRepo repository.IUserRepository, gameRepo repository.IGameRepository, puzzleRepo repository.IPuzzleRepository, taskRepo repository.ITaskRepository, puzzleQueueRepo repository.IPuzzleQueueRepository, learningQueueRepo repository.ILearningQueueRepository, userDailyStatsRepo repository.IUserDailyStatsRepository, userEloRepo repository.IUserEloRepository, userSprintDailyStatsRepo repository.IUserSprintDailyStatsRepository, chessVerifier verification.IChessProfileVerifier, eventService *service.EventService) *UserHandler {
	return &UserHandler{
		userRepo:                 userRepo,
		gameRepo:                 gameRepo,
		puzzleRepo:               puzzleRepo,
		taskRepo:                 taskRepo,
		puzzleQueueRepo:          puzzleQueueRepo,
		learningQueueRepo:        learningQueueRepo,
		userDailyStatsRepo:       userDailyStatsRepo,
		userEloRepo:              userEloRepo,
		userSprintDailyStatsRepo: userSprintDailyStatsRepo,
		chessVerifier:            chessVerifier,
		eventService:             eventService,
	}
}

// UserRoutes creates a router for user endpoints
func UserRoutes(userRepo repository.IUserRepository, gameRepo repository.IGameRepository, puzzleRepo repository.IPuzzleRepository, taskRepo repository.ITaskRepository, puzzleQueueRepo repository.IPuzzleQueueRepository, learningQueueRepo repository.ILearningQueueRepository, userDailyStatsRepo repository.IUserDailyStatsRepository, userEloRepo repository.IUserEloRepository, userSprintDailyStatsRepo repository.IUserSprintDailyStatsRepository, chessVerifier verification.IChessProfileVerifier, eventService *service.EventService) http.Handler {
	h := NewUserHandler(userRepo, gameRepo, puzzleRepo, taskRepo, puzzleQueueRepo, learningQueueRepo, userDailyStatsRepo, userEloRepo, userSprintDailyStatsRepo, chessVerifier, eventService)
	r := chi.NewRouter()

	r.Get("/", h.ListUsers)
	r.Post("/", h.CreateUser)
	r.Get("/{id}", h.GetUser)
	r.Put("/{id}", h.UpdateUser)

	// Add chess profile routes
	r.Get("/{id}/chess-profiles", h.ListUserChessProfiles)
	r.Put("/{id}/chess-profiles/{profileID}/refresh", h.RefreshChessProfile(gameRepo))

	return r
}

type CreateUserRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type UserResponse struct {
	ID               string                        `json:"id"`
	Email            string                        `json:"email"`
	FirebaseUID      *string                       `json:"firebase_uid,omitempty"`
	RegisteredAt     time.Time                     `json:"registered_at"`
	UpdatedAt        time.Time                     `json:"updated_at"`
	LastSignInAt     *time.Time                    `json:"last_sign_in_at,omitempty"`
	ChessProfiles    []models.ChessProfile         `json:"chess_profiles,omitempty"`
	DailyStats       []models.UserDailyStats       `json:"daily_stats,omitempty"`
	Elos             []models.UserElo              `json:"elos,omitempty"`
	SprintDailyStats []models.UserSprintDailyStats `json:"sprint_daily_stats"`
}

// Render writes the response as JSON
// Note: Default render.JSON is often sufficient, custom Render might not be needed
// unless specific response structuring is required.
// func (u UserResponse) Render(w http.ResponseWriter, r *http.Request) error {
// 	render.JSON(w, r, u)
// 	return nil
// }

// ListUsers returns all users
// The userRepo.GetAll should already preload ChessProfiles
func (h *UserHandler) ListUsers(w http.ResponseWriter, r *http.Request) {
	// In a real application, you'd want to add pagination here
	users, err := h.userRepo.GetAll(r.Context())
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve users")
		return
	}

	// We might want a specific ListUserResponse struct array here
	// to avoid exposing everything from models.User, but for now,
	// returning the model directly (which includes profiles) is okay.
	render.JSON(w, r, users)
}

// CreateUser handles creating a new user
func (h *UserHandler) CreateUser(w http.ResponseWriter, r *http.Request) {
	var req CreateUserRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.Email == "" || req.Password == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Email and password are required")
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to process password")
		return
	}

	// Create user model
	user := &models.User{
		Email:        req.Email,
		PasswordHash: string(hashedPassword),
	}

	// Save to database (Create method handles setting UserID on profiles)
	if err := h.userRepo.Create(r.Context(), user); err != nil {
		// TODO: Handle specific errors like duplicate email/profile
		apiError(w, r, http.StatusInternalServerError, err, "Failed to create user")
		return
	}

	// Prepare response (re-fetch user to get generated IDs, including profile IDs)
	createdUser, err := h.userRepo.GetByID(r.Context(), user.ID)
	if err != nil {
		// Log error, but might still return basic info if user was technically created
		apiError(w, r, http.StatusInternalServerError, err, "User created but failed to fetch final details")
		return
	}

	resp := UserResponse{
		ID:            createdUser.ID,
		Email:         createdUser.Email,
		FirebaseUID:   createdUser.FirebaseUID,
		RegisteredAt:  createdUser.RegisteredAt,
		UpdatedAt:     createdUser.UpdatedAt,
		LastSignInAt:  createdUser.LastSignInAt,
		ChessProfiles: createdUser.ChessProfiles, // Include profiles in response
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, resp)
}

// GetUser handles retrieving a user by ID
func (h *UserHandler) GetUser(w http.ResponseWriter, r *http.Request) {
	userID := chi.URLParam(r, "id")
	if userID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "User ID is required")
		return
	}

	user, err := h.userRepo.GetByID(r.Context(), userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "User not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve user")
		}
		return
	}

	// Prepare response
	resp := UserResponse{
		ID:            user.ID,
		Email:         user.Email,
		FirebaseUID:   user.FirebaseUID,
		RegisteredAt:  user.RegisteredAt,
		UpdatedAt:     user.UpdatedAt,
		LastSignInAt:  user.LastSignInAt,
		ChessProfiles: user.ChessProfiles, // Include profiles fetched by GetByID
	}

	render.JSON(w, r, resp)
}

type UpdateUserRequest struct {
	Email    *string `json:"email,omitempty"` // Use pointers to distinguish between empty and not provided
	Password *string `json:"password,omitempty"`
}

// UpdateUser handles updating a user
func (h *UserHandler) UpdateUser(w http.ResponseWriter, r *http.Request) {
	userID := chi.URLParam(r, "id")
	if userID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "User ID is required")
		return
	}

	// Get the existing user
	user, err := h.userRepo.GetByID(r.Context(), userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "User not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve user for update")
		}
		return
	}
	// Clear the existing profiles, update does not handle this
	user.ChessProfiles = nil

	// Parse request body
	var req UpdateUserRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	updated := false
	// Update fields if provided
	if req.Email != nil {
		if *req.Email == "" {
			apiError(w, r, http.StatusBadRequest, nil, "Email cannot be empty")
			return
		}
		user.Email = *req.Email
		updated = true
	}

	if req.Password != nil {
		if *req.Password == "" {
			apiError(w, r, http.StatusBadRequest, nil, "Password cannot be empty")
			return
		}
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(*req.Password), bcrypt.DefaultCost)
		if err != nil {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to process password")
			return
		}
		user.PasswordHash = string(hashedPassword)
		updated = true
	}

	if !updated {
		// Fetch fresh data in case something changed concurrently?
		// For now, just return 200 with current data if nothing submitted.
		updatedUser, err := h.userRepo.GetByID(r.Context(), user.ID)
		if err != nil {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve user after no-op update")
			return
		}
		resp := UserResponse{
			ID:            updatedUser.ID,
			Email:         updatedUser.Email,
			FirebaseUID:   updatedUser.FirebaseUID,
			RegisteredAt:  updatedUser.RegisteredAt,
			UpdatedAt:     updatedUser.UpdatedAt,
			LastSignInAt:  updatedUser.LastSignInAt,
			ChessProfiles: updatedUser.ChessProfiles,
		}
		render.JSON(w, r, resp)
		return
	}

	// Save to database (Update handles associations via Save behavior)
	if err := h.userRepo.Update(r.Context(), user); err != nil {
		// TODO: Handle specific errors like duplicate email/profile constraint violations
		apiError(w, r, http.StatusInternalServerError, err, "Failed to update user")
		return
	}

	// Fetch the updated user to ensure response reflects persisted state
	updatedUser, err := h.userRepo.GetByID(r.Context(), user.ID)
	if err != nil {
		// Log the error, but the update likely succeeded. Return 500 as we can't confirm the final state.
		apiError(w, r, http.StatusInternalServerError, err, "User updated but failed to fetch final details")
		return
	}

	// Prepare response
	resp := UserResponse{
		ID:            updatedUser.ID,
		Email:         updatedUser.Email,
		FirebaseUID:   updatedUser.FirebaseUID,
		RegisteredAt:  updatedUser.RegisteredAt,
		UpdatedAt:     updatedUser.UpdatedAt,
		LastSignInAt:  updatedUser.LastSignInAt,
		ChessProfiles: updatedUser.ChessProfiles,
	}

	render.JSON(w, r, resp)
}

// --- Handlers for /users/me ---

// Helper to get user ID from context
func getUserIDFromContext(r *http.Request) (string, error) {
	userID, ok := r.Context().Value(middleware.UserIDKey).(string)
	if !ok || userID == "" {
		return "", errors.New("user ID not found in context or is invalid")
	}
	return userID, nil
}

// GetMe handles GET /api/v1/users/me
// Returns the authenticated user's details along with their chess profiles and daily stats.
// Query parameters:
// - days: number of days to include in daily stats (default: 7, max: 365)
// - start_date: start date for daily stats in YYYY-MM-DD format
// - end_date: end date for daily stats in YYYY-MM-DD format
func (h *UserHandler) GetMe(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		// Use a standard unauthorized error response
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	user, err := h.userRepo.GetByID(r.Context(), userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "User not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve user details")
		}
		return
	}

	// Parse query parameters for daily stats
	var startDate, endDate *time.Time

	// Check if specific date range is provided
	startDateStr := r.URL.Query().Get("start_date")
	endDateStr := r.URL.Query().Get("end_date")

	if startDateStr != "" && endDateStr != "" {
		// Parse specific date range
		start, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			apiError(w, r, http.StatusBadRequest, err, "Invalid start_date format. Use YYYY-MM-DD")
			return
		}
		end, err := time.Parse("2006-01-02", endDateStr)
		if err != nil {
			apiError(w, r, http.StatusBadRequest, err, "Invalid end_date format. Use YYYY-MM-DD")
			return
		}
		startDate = &start
		endDate = &end
	} else {
		// Use days parameter (default: 7 days)
		daysStr := r.URL.Query().Get("days")
		days := 7 // default
		if daysStr != "" {
			if parsedDays, err := strconv.Atoi(daysStr); err == nil && parsedDays > 0 && parsedDays <= 365 {
				days = parsedDays
			} else {
				apiError(w, r, http.StatusBadRequest, nil, "Invalid days parameter. Must be between 1 and 365")
				return
			}
		}

		// Calculate date range for the last N days
		now := time.Now()
		// Include tomorrow to account for timezone differences between event creation and date calculation
		end := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC).AddDate(0, 0, 1)
		start := end.AddDate(0, 0, -days) // Include today and tomorrow, so -days (not -days+1)
		startDate = &start
		endDate = &end
	}

	// Determine the limit for daily stats
	dailyStatsLimit := 365 // Default max
	if r.URL.Query().Get("start_date") == "" && r.URL.Query().Get("end_date") == "" {
		// If using days parameter, limit results to that number
		daysStr := r.URL.Query().Get("days")
		if daysStr != "" {
			if parsedDays, err := strconv.Atoi(daysStr); err == nil && parsedDays > 0 && parsedDays <= 365 {
				dailyStatsLimit = parsedDays
			}
		} else {
			dailyStatsLimit = 7 // Default 7 days
		}
	}

	// Fetch daily stats
	dailyStats, _, err := h.userDailyStatsRepo.ListByUserID(r.Context(), userID, startDate, endDate, 0, dailyStatsLimit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve daily stats")
		return
	}

	// Fetch user ELOs
	userElos, err := h.userEloRepo.GetByUserID(r.Context(), userID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve user ELOs")
		return
	}

	// Fetch sprint daily stats (using the same date range as regular daily stats)
	sprintDailyStats, _, err := h.userSprintDailyStatsRepo.ListByUserID(r.Context(), userID, nil, startDate, endDate, 0, 365) // Get up to 365 days, no ELO type filter
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve sprint daily stats")
		return
	}

	// Use the existing UserResponse struct, which includes ChessProfiles, DailyStats, Elos, and SprintDailyStats
	resp := UserResponse{
		ID:               user.ID,
		Email:            user.Email,
		FirebaseUID:      user.FirebaseUID,
		RegisteredAt:     user.RegisteredAt,
		UpdatedAt:        user.UpdatedAt,
		LastSignInAt:     user.LastSignInAt,
		ChessProfiles:    user.ChessProfiles, // GetByID should preload these
		DailyStats:       dailyStats,
		Elos:             userElos,
		SprintDailyStats: sprintDailyStats,
	}

	render.JSON(w, r, resp)
}

// ListMyChessProfiles handles GET /api/v1/users/me/chess-profiles
// Returns only the chess profiles for the authenticated user.
func (h *UserHandler) ListMyChessProfiles(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// We can optimize this by adding a repository method GetChessProfilesByUserID
	// if it doesn't exist, but for now, fetching the user works.
	user, err := h.userRepo.GetByID(r.Context(), userID) // Still fetches the whole user
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// If user not found, they have no profiles
			render.JSON(w, r, []models.ChessProfile{}) // Return empty list
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve user to list profiles")
		}
		return
	}

	// Return only the ChessProfiles slice
	render.JSON(w, r, user.ChessProfiles)
}

// CreateMyChessProfileRequest defines the expected JSON body for creating a profile.
type CreateMyChessProfileRequest struct {
	Platform string `json:"platform"`
	Username string `json:"username"`
	// Add other relevant fields from models.ChessProfile if needed
}

// CreateMyChessProfile handles POST /api/v1/users/me/chess-profiles
// Creates a new chess profile for the authenticated user.
func (h *UserHandler) CreateMyChessProfile(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	var req CreateMyChessProfileRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.Platform == "" || req.Username == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Platform and username are required")
		return
	}

	// Verify chess profile exists on the platform
	platform := models.ChessPlatform(req.Platform)
	if err := h.chessVerifier.VerifyProfile(r.Context(), platform, req.Username); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Chess profile verification failed")
		return
	}

	// Check profile limit (max 2)
	count, err := h.userRepo.CountChessProfiles(r.Context(), userID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to count existing profiles")
		return
	}
	if count >= 2 {
		apiError(w, r, http.StatusForbidden, nil, "User cannot have more than 2 chess profiles")
		return
	}

	// Create profile model
	profile := &models.ChessProfile{
		UserID:   userID, // Set UserID from authenticated user
		Platform: models.ChessPlatform(req.Platform),
		Username: req.Username,
		// Initialize other fields if necessary
	}

	// Add profile using repository method
	if err := h.userRepo.AddChessProfile(r.Context(), userID, profile); err != nil {
		// Handle potential errors like duplicate profile (platform/username for user)
		apiError(w, r, http.StatusInternalServerError, err, "Failed to add chess profile") // Consider 409 Conflict for duplicates
		return
	}

	// --- Task Creation ---
	taskData := models.FetchChessGamesData{
		UserID:         userID,
		ChessProfileID: profile.ID, // Use the ID generated by AddChessProfile
	}
	taskDataJSON, err := json.Marshal(taskData)
	if err != nil {
		// Log error, but don't fail the profile creation request
		logger.FromContext(r.Context()).Error().Err(err).Str("user_id", userID).Str("profile_id", profile.ID).Msg("Error marshaling task data for FetchChessGamesTask")
	} else {
		task := &models.Task{
			ID:       uuid.NewString(),
			UserID:   userID,
			TaskType: models.FetchChessGamesTask,
			TaskData: taskDataJSON,
			Status:   models.TaskStatusPending,
		}
		if err := h.taskRepo.Create(r.Context(), task); err != nil {
			// Log error, but don't fail the profile creation request
			logger.FromContext(r.Context()).Error().Err(err).Str("user_id", userID).Str("profile_id", profile.ID).Msg("Error creating FetchChessGamesTask")
		} else {
			logger.FromContext(r.Context()).Info().Str("task_id", task.ID).Str("user_id", userID).Str("profile_id", profile.ID).Msg("Created FetchChessGamesTask")
		}
	}
	// --- End Task Creation ---

	// Return the newly created profile
	render.Status(r, http.StatusCreated)
	render.JSON(w, r, profile) // Return the created profile, including its generated ID
}

// DeleteMyChessProfile handles DELETE /api/v1/users/me/chess-profiles/{profileID}
// Deletes a specific chess profile for the authenticated user.
func (h *UserHandler) DeleteMyChessProfile(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Get profile ID from URL path
	profileID := chi.URLParam(r, "profileID")
	if profileID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Profile ID is required")
		return
	}

	// Fetch profile details first to ensure we have the info needed for game deletion
	profile, err := h.userRepo.GetChessProfileByID(r.Context(), profileID)
	if err == nil && profile.UserID == userID {
		// Only delete games if profile exists and belongs to the user
		// Create a filter to get only the relevant games
		gameFilter := repository.GameFilter{
			Platform:      &profile.Platform,
			ChessUsername: &profile.Username,
		}

		// Delete games associated with this profile
		// We don't need to delete puzzles as they will be deleted via cascade from games
		deletedCount, err := h.gameRepo.DeleteByFilter(r.Context(), userID, gameFilter)
		if err != nil {
			logger.FromContext(r.Context()).Error().Err(err).Str("user_id", userID).Str("profile_id", profileID).Msg("Error deleting games for profile")
			// Continue with profile deletion even if game deletion fails
		} else {
			logger.FromContext(r.Context()).Info().Int64("deleted_count", deletedCount).Str("user_id", userID).Str("profile_id", profileID).Msg("Deleted games for profile")
		}
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// Log error if fetching profile failed for reasons other than not found
		logger.FromContext(r.Context()).Error().Err(err).Str("profile_id", profileID).Msg("Error fetching profile before deletion")
	}

	// Try to delete the profile
	if err := h.userRepo.DeleteChessProfile(r.Context(), userID, profileID); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// If record not found, the profile doesn't exist or doesn't belong to the user
			apiError(w, r, http.StatusNotFound, nil, "Chess profile not found")
		} else {
			// Handle other potential DB errors
			apiError(w, r, http.StatusInternalServerError, err, "Failed to delete chess profile")
		}
		return
	}

	// Profile deleted successfully, return 204 No Content
	w.WriteHeader(http.StatusNoContent)
}

// ListUserChessProfiles handles listing a user's chess profiles (admin only)
func (h *UserHandler) ListUserChessProfiles(w http.ResponseWriter, r *http.Request) {
	userID := chi.URLParam(r, "id")
	if userID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "User ID is required")
		return
	}

	// Check if user exists
	_, err := h.userRepo.GetByID(r.Context(), userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "User not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve user")
		}
		return
	}

	// Get user's chess profiles
	profiles, err := h.userRepo.GetChessProfilesByUserID(r.Context(), userID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve chess profiles")
		return
	}

	render.JSON(w, r, profiles)
}

// RefreshChessProfile handles refreshing a user's chess profile stats from the games table (admin only)
func (h *UserHandler) RefreshChessProfile(gameRepo repository.IGameRepository) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		userID := chi.URLParam(r, "id")
		if userID == "" {
			apiError(w, r, http.StatusBadRequest, nil, "User ID is required")
			return
		}

		profileID := chi.URLParam(r, "profileID")
		if profileID == "" {
			apiError(w, r, http.StatusBadRequest, nil, "Profile ID is required")
			return
		}

		// Check if profile exists and belongs to the user
		profile, err := h.userRepo.GetChessProfileByID(r.Context(), profileID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				apiError(w, r, http.StatusNotFound, nil, "Chess profile not found")
			} else {
				apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve chess profile")
			}
			return
		}

		if profile.UserID != userID {
			apiError(w, r, http.StatusForbidden, nil, "Profile does not belong to user")
			return
		}

		// --- Refetch games using filtered ListByUserID ---
		// Create a filter to get only the relevant games
		gameFilter := repository.GameFilter{
			Platform:      &profile.Platform,
			ChessUsername: &profile.Username,
		}

		// Fetch only the first game matching the filter, ordered by game time descending
		// This gives us the latest game and the total count efficiently.
		// We only need the latest game time and the total count.
		matchingGames, totalCount, err := h.gameRepo.ListByUserID(r.Context(), userID, gameFilter, 0, 1)
		if err != nil {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to list games for profile refresh")
			return
		}
		// --- End Refetch games ---

		// Update profile stats
		gameCount := int(totalCount) // Total count comes directly from the filtered query
		now := time.Now()

		// Get the latest game time from the fetched game (if any)
		var latestGameTime *time.Time
		if len(matchingGames) > 0 {
			latestGameTime = &matchingGames[0].GameTime
		}

		// Update profile with new values
		updates := map[string]interface{}{
			"games_fetched":        gameCount,
			"last_game_fetched_at": &now,
			"updated_at":           now,
		}

		// Only update last_game_played_at if we found matching games
		if latestGameTime != nil {
			updates["last_game_played_at"] = latestGameTime
		}

		if err := h.userRepo.UpdateChessProfileFields(r.Context(), profile.ID, updates); err != nil {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to update chess profile fields")
			return
		}

		// Get the updated profile
		updatedProfile, err := h.userRepo.GetChessProfileByID(r.Context(), profile.ID)
		if err != nil {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve updated chess profile")
			return
		}

		render.JSON(w, r, updatedProfile)
	}
}

// ListMyGames handles listing games for the authenticated user with filtering and pagination
func (h *UserHandler) ListMyGames(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Parse pagination
	offset, limit, err := parsePaginationParams(r)
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err is already a user-friendly string here
		return
	}

	// Apply filters based on query parameters
	var gameFilter repository.GameFilter
	if platform := r.URL.Query().Get("platform"); platform != "" {
		// Validate platform value
		parsedPlatform := models.ChessPlatform(platform)
		if parsedPlatform != models.ChessDotCom && parsedPlatform != models.LichessOrg {
			apiError(w, r, http.StatusBadRequest, nil, fmt.Sprintf("Invalid platform: %s", platform))
			return
		}
		gameFilter.Platform = &parsedPlatform
	}
	if username := r.URL.Query().Get("username"); username != "" {
		gameFilter.ChessUsername = &username
	}
	startTime, err := parseTimeParam(r, "start_time")
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err is already a user-friendly string here
		return
	}
	gameFilter.StartTime = startTime

	endTime, err := parseTimeParam(r, "end_time")
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err is already a user-friendly string here
		return
	}
	gameFilter.EndTime = endTime

	// Fetch games
	games, totalCount, err := h.gameRepo.ListByUserID(r.Context(), userID, gameFilter, offset, limit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to list games")
		return
	}

	// Create response slice with decompressed PGN
	gameResponses := make([]GameResponse, 0, len(games))
	for _, game := range games {
		decompressedPGN, err := game.GetPGN()
		if err != nil {
			apiError(w, r, http.StatusInternalServerError, err, fmt.Sprintf("Failed to process PGN for game %s", game.ID))
			return
		}
		// Create the response object, embedding the original game and adding the PGN
		resp := GameResponse{
			Game: game,
			PGN:  string(decompressedPGN), // Convert byte slice to string
		}
		// Note: CompressedPGN has json:"-" tag so it won't be serialized in the response

		gameResponses = append(gameResponses, resp)
	}

	// Build response
	response := map[string]interface{}{
		"games":       gameResponses, // Use the slice with decompressed PGN
		"total_count": totalCount,
		"offset":      offset,
		"limit":       limit,
	}

	render.JSON(w, r, response)
}

// ListMyPuzzles handles listing puzzles for the authenticated user with filtering and pagination
func (h *UserHandler) ListMyPuzzles(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Parse pagination
	offset, limit, err := parsePaginationParams(r)
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err is already a user-friendly string here
		return
	}

	// Apply filters based on query parameters
	var puzzleFilter repository.PuzzleFilter
	if tags := r.URL.Query().Get("tags"); tags != "" {
		puzzleFilter.Tags = strings.Split(tags, ",")
	}
	gameStartTime, err := parseTimeParam(r, "game_start_time")
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err is already a user-friendly string here
		return
	}
	puzzleFilter.GameStartTime = gameStartTime

	gameEndTime, err := parseTimeParam(r, "game_end_time")
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err is already a user-friendly string here
		return
	}
	puzzleFilter.GameEndTime = gameEndTime

	// Fetch puzzles
	puzzles, totalCount, err := h.puzzleRepo.ListByUserID(r.Context(), userID, puzzleFilter, offset, limit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to list puzzles")
		return
	}

	// Build response
	response := map[string]interface{}{
		"puzzles":     puzzles,
		"total_count": totalCount,
		"offset":      offset,
		"limit":       limit,
	}

	render.JSON(w, r, response)
}

// PostPuzzleAttemptRequest defines the expected JSON body for posting a puzzle attempt
type PostPuzzleAttemptRequest struct {
	Moves          []string `json:"moves"`                  // User's moves in the puzzle
	Solved         bool     `json:"solved"`                 // Whether the puzzle was solved
	TimeSpent      int      `json:"time_spent"`             // Time spent in seconds
	AttemptType    string   `json:"attempt_type,omitempty"` // "regular" or "arrow_duel"
	IsQueueAttempt bool     `json:"is_queue_attempt"`       // Flag to enable queue scheduling
	IsDisliked     *bool    `json:"is_disliked,omitempty"`  // Optional: whether user dislikes this puzzle

	// Arrow-duel specific fields (only used when AttemptType is "arrow_duel")
	CandidateMoves []string `json:"candidate_moves,omitempty"` // [blunder_move, correct_move]
	ChosenMove     *string  `json:"chosen_move,omitempty"`     // Move chosen by player
}

// PostPuzzleAttempt handles POST /api/v1/users/me/puzzles/{puzzleID}/attempts
// Records a puzzle attempt and creates a puzzle event for stats tracking
func (h *UserHandler) PostPuzzleAttempt(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Get puzzle ID from URL path
	puzzleID := chi.URLParam(r, "puzzleID")
	if puzzleID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Puzzle ID is required")
		return
	}

	// Parse request body
	var req PostPuzzleAttemptRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.TimeSpent < 0 {
		apiError(w, r, http.StatusBadRequest, nil, "Time spent cannot be negative")
		return
	}

	// Validate arrow-duel specific fields
	attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
	if attemptType == models.AttemptTypeArrowDuel {
		// Validate candidate moves
		if len(req.CandidateMoves) != 2 {
			apiError(w, r, http.StatusBadRequest, nil, "Arrow-duel attempts must have exactly 2 candidate moves")
			return
		}

		// Validate chosen move
		if req.ChosenMove == nil {
			apiError(w, r, http.StatusBadRequest, nil, "Arrow-duel attempts must specify chosen_move")
			return
		}

		// Validate that chosen move is one of the candidate moves
		chosenMove := *req.ChosenMove
		if chosenMove != req.CandidateMoves[0] && chosenMove != req.CandidateMoves[1] {
			apiError(w, r, http.StatusBadRequest, nil, "chosen_move must be one of the candidate_moves")
			return
		}
	}

	// Verify that the puzzle exists and belongs to the user
	puzzle, err := h.puzzleRepo.GetByID(r.Context(), puzzleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Puzzle not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve puzzle")
		}
		return
	}

	// Check if the puzzle belongs to the user
	if puzzle.UserID != userID {
		apiError(w, r, http.StatusForbidden, nil, "Puzzle does not belong to the current user")
		return
	}

	// Create puzzle event data
	puzzleEventData := models.PuzzleEventData{
		PuzzleID:       puzzleID,
		PuzzleType:     models.PuzzleTypeUser, // User-generated puzzle from their own games
		AttemptType:    attemptType,
		Solved:         req.Solved,
		TimeSpent:      req.TimeSpent,
		MovesPlayed:    req.Moves,
		IsDisliked:     req.IsDisliked,
		CandidateMoves: req.CandidateMoves, // Will be empty for regular attempts
		ChosenMove:     req.ChosenMove,     // Will be nil for regular attempts
	}

	// Create the puzzle event
	if err := h.eventService.CreatePuzzleEvent(r.Context(), userID, puzzleEventData); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to record puzzle attempt")
		return
	}

	// Update puzzle queue if this is a queue attempt
	if req.IsQueueAttempt {
		if err := h.puzzleQueueRepo.UpdateAfterAttempt(r.Context(), userID, puzzleID, req.Solved); err != nil {
			// Log the error but don't fail the request since the attempt was already recorded
			logger.FromContext(r.Context()).Error().Err(err).
				Str("user_id", userID).
				Str("puzzle_id", puzzleID).
				Bool("solved", req.Solved).
				Msg("Failed to update puzzle queue after attempt")
		}
	}

	// Return success response
	w.WriteHeader(http.StatusCreated)
}

// PostLichessPuzzleAttemptRequest defines the expected JSON body for posting a Lichess puzzle attempt
type PostLichessPuzzleAttemptRequest struct {
	Moves             []string `json:"moves"`                  // User's moves in the puzzle
	Solved            bool     `json:"solved"`                 // Whether the puzzle was solved
	TimeSpent         int      `json:"time_spent"`             // Time spent in seconds
	AttemptType       string   `json:"attempt_type,omitempty"` // "regular" or "arrow_duel"
	IsLearningAttempt bool     `json:"is_learning_attempt"`    // Flag to enable learning queue scheduling
	IsDisliked        *bool    `json:"is_disliked,omitempty"`  // Optional: whether user dislikes this puzzle

	// Arrow-duel specific fields (only used when AttemptType is "arrow_duel")
	CandidateMoves []string `json:"candidate_moves,omitempty"` // [blunder_move, correct_move]
	ChosenMove     *string  `json:"chosen_move,omitempty"`     // Move chosen by player
}

// PostLichessPuzzleAttempt handles POST /api/v1/users/me/lichess-puzzles/{puzzleID}/attempts
// Records a Lichess puzzle attempt and creates a puzzle event for stats tracking
func (h *UserHandler) PostLichessPuzzleAttempt(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Get puzzle ID from URL path
	puzzleID := chi.URLParam(r, "puzzleID")
	if puzzleID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Puzzle ID is required")
		return
	}

	// Parse request body
	var req PostLichessPuzzleAttemptRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.TimeSpent < 0 {
		apiError(w, r, http.StatusBadRequest, nil, "Time spent cannot be negative")
		return
	}

	// Validate arrow-duel specific fields
	attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
	if attemptType == models.AttemptTypeArrowDuel {
		// Validate candidate moves
		if len(req.CandidateMoves) != 2 {
			apiError(w, r, http.StatusBadRequest, nil, "Arrow-duel attempts must have exactly 2 candidate moves")
			return
		}

		// Validate chosen move
		if req.ChosenMove == nil {
			apiError(w, r, http.StatusBadRequest, nil, "Arrow-duel attempts must specify chosen_move")
			return
		}

		// Validate that chosen move is one of the candidate moves
		chosenMove := *req.ChosenMove
		if chosenMove != req.CandidateMoves[0] && chosenMove != req.CandidateMoves[1] {
			apiError(w, r, http.StatusBadRequest, nil, "chosen_move must be one of the candidate_moves")
			return
		}
	}

	// Create puzzle event data for Lichess puzzle
	puzzleEventData := models.PuzzleEventData{
		PuzzleID:       puzzleID,
		PuzzleType:     models.PuzzleTypeLichess, // Lichess puzzle
		AttemptType:    attemptType,
		Solved:         req.Solved,
		TimeSpent:      req.TimeSpent,
		MovesPlayed:    req.Moves,
		IsDisliked:     req.IsDisliked,
		CandidateMoves: req.CandidateMoves, // Will be empty for regular attempts
		ChosenMove:     req.ChosenMove,     // Will be nil for regular attempts
	}

	// Create the puzzle event
	if err := h.eventService.CreatePuzzleEvent(r.Context(), userID, puzzleEventData); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to record Lichess puzzle attempt")
		return
	}

	// Update learning queue if this is a learning attempt
	if req.IsLearningAttempt {
		if err := h.learningQueueRepo.UpdateAfterAttempt(r.Context(), userID, puzzleID, req.Solved, string(attemptType), req.TimeSpent); err != nil {
			// Log the error but don't fail the request since the attempt was already recorded
			logger.FromContext(r.Context()).Error().Err(err).
				Str("user_id", userID).
				Str("puzzle_id", puzzleID).
				Bool("solved", req.Solved).
				Str("attempt_type", string(attemptType)).
				Int("time_spent", req.TimeSpent).
				Msg("Failed to update learning queue after attempt")
		}
	}

	// Return success response
	w.WriteHeader(http.StatusCreated)
}

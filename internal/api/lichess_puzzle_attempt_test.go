package api

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/chessticize/chessticize-server/internal/verification"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupLichessPuzzleAttemptTest(t *testing.T) (repository.IUserRepository, repository.IEventRepository, http.Handler) {
	// Create a fake DB for testing
	db := fake.NewDB(t)

	// Create repositories
	userRepo := fake.NewUserRepository(db)
	gameRepo := fake.NewGameRepository(db)
	puzzleRepo := fake.NewPuzzleRepository(db)
	taskRepo := fake.NewFakeTaskRepository(db)
	puzzleQueueRepo := fake.NewFakePuzzleQueueRepository(db)
	eventRepo := fake.NewEventRepository(db.DB)
	userDailyStatsRepo := fake.NewUserDailyStatsRepository(db.DB)

	// Create services
	jwtConfig := testutils.GetTestJWTConfig()
	sessionTokenConfig := config.SessionTokenConfig{ExpiryDays: 30}
	eventService := service.NewEventService(eventRepo)
	chessVerifier := verification.NewChessProfileVerifier()

	// Create test config
	testConfig := &config.Config{
		JWT:          jwtConfig,
		SessionToken: sessionTokenConfig,
	}

	// Create the router
	r := chi.NewRouter()
	r.Route("/api/v1", func(r chi.Router) {
		r.Route("/users/me", func(r chi.Router) {
			r.Use(middleware.JWTAuth(testConfig.JWT))
			userEloRepo := repository.NewUserEloRepository(testutils.GetFakeDB(t).DB)
			userSprintDailyStatsRepo := repository.NewUserSprintDailyStatsRepository(testutils.GetFakeDB(t).DB)
			userLearningDailyStatsRepo := fake.NewFakeUserLearningDailyStatsRepository(testutils.GetFakeDB(t))
			learningQueueRepo := fake.NewFakeLearningQueueRepository(testutils.GetFakeDB(t), userLearningDailyStatsRepo)
			hUser := NewUserHandler(userRepo, gameRepo, puzzleRepo, taskRepo, puzzleQueueRepo, learningQueueRepo, userDailyStatsRepo, userEloRepo, userSprintDailyStatsRepo, chessVerifier, eventService)
			r.Post("/lichess-puzzles/{puzzleID}/attempts", hUser.PostLichessPuzzleAttempt)
		})
	})

	return userRepo, eventRepo, r
}

func TestPostLichessPuzzleAttempt(t *testing.T) {
	userRepo, eventRepo, router := setupLichessPuzzleAttemptTest(t)

	// Create test user
	testUser := testutils.CreateTestUser(t, userRepo)

	t.Run("PostLichessPuzzleAttempt_Success_Solved", func(t *testing.T) {
		// Create Lichess puzzle attempt request
		attemptReq := PostLichessPuzzleAttemptRequest{
			Moves:     []string{"Qh5", "g6", "Qxf7#"},
			Solved:    true,
			TimeSpent: 45,
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/lichess-puzzles/lichess-puzzle-123/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Verify event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), testUser.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypePuzzle},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, testUser.ID, event.UserID)
		assert.Equal(t, models.EventTypePuzzle, event.EventType)

		// Verify event data
		var eventData models.PuzzleEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)

		assert.Equal(t, "lichess-puzzle-123", eventData.PuzzleID)
		assert.Equal(t, models.PuzzleTypeLichess, eventData.PuzzleType)
		assert.True(t, eventData.Solved)
		assert.Equal(t, 45, eventData.TimeSpent)
		assert.Equal(t, []string{"Qh5", "g6", "Qxf7#"}, eventData.MovesPlayed) // Actual moves in the request
	})

	t.Run("PostLichessPuzzleAttempt_Success_Failed", func(t *testing.T) {
		// Create another test user to avoid event conflicts
		testUser2 := testutils.CreateTestUser(t, userRepo)

		// Create Lichess puzzle attempt request for failed attempt
		attemptReq := PostLichessPuzzleAttemptRequest{
			Moves:     []string{"Qh4", "g6"},
			Solved:    false,
			TimeSpent: 120,
		}

		token := testutils.GenerateUserToken(t, testUser2.ID, testUser2.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/lichess-puzzles/lichess-puzzle-456/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Verify event was created with correct data
		events, _, err := eventRepo.ListByUserID(context.TODO(), testUser2.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypePuzzle},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		var eventData models.PuzzleEventData
		err = json.Unmarshal(events[0].EventData, &eventData)
		require.NoError(t, err)

		assert.Equal(t, "lichess-puzzle-456", eventData.PuzzleID)
		assert.Equal(t, models.PuzzleTypeLichess, eventData.PuzzleType)
		assert.False(t, eventData.Solved)
		assert.Equal(t, 120, eventData.TimeSpent)
		assert.Equal(t, []string{"Qh4", "g6"}, eventData.MovesPlayed)
	})

	t.Run("PostLichessPuzzleAttempt_MissingPuzzleID", func(t *testing.T) {
		attemptReq := PostLichessPuzzleAttemptRequest{
			Moves:     []string{"e4"},
			Solved:    true,
			TimeSpent: 30,
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		// Use empty puzzle ID in URL
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/lichess-puzzles//attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response - this should result in a 400 due to missing puzzle ID
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("PostLichessPuzzleAttempt_InvalidTimeSpent", func(t *testing.T) {
		attemptReq := PostLichessPuzzleAttemptRequest{
			Moves:     []string{"e4"},
			Solved:    true,
			TimeSpent: -10, // Invalid negative time
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/lichess-puzzles/lichess-puzzle-789/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("PostLichessPuzzleAttempt_Unauthorized", func(t *testing.T) {
		attemptReq := PostLichessPuzzleAttemptRequest{
			Moves:     []string{"e4"},
			Solved:    true,
			TimeSpent: 30,
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/lichess-puzzles/lichess-puzzle-789/attempts", attemptReq)
		// Don't set Authorization header
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})
}

package api

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/chessticize/chessticize-server/internal/verification"
	"github.com/go-chi/chi/v5"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupPuzzleQueueTest(t *testing.T) (repository.IUserRepository, repository.IPuzzleRepository, repository.IPuzzleQueueRepository, http.Handler) {
	// Create a fake DB for testing
	db := fake.NewDB(t)

	// Create repositories
	userRepo := fake.NewUserRepository(db)
	gameRepo := fake.NewGameRepository(db)
	puzzleRepo := fake.NewPuzzleRepository(db)
	taskRepo := fake.NewFakeTaskRepository(db)
	puzzleQueueRepo := fake.NewFakePuzzleQueueRepository(db)
	eventRepo := fake.NewEventRepository(db.DB)
	userDailyStatsRepo := fake.NewUserDailyStatsRepository(db.DB)

	// Create services
	jwtConfig := testutils.GetTestJWTConfig()
	sessionTokenConfig := config.SessionTokenConfig{ExpiryDays: 30}
	eventService := service.NewEventService(eventRepo)
	chessVerifier := verification.NewChessProfileVerifier()

	// Create test config
	testConfig := &config.Config{
		JWT:          jwtConfig,
		SessionToken: sessionTokenConfig,
	}

	// Create the router
	r := chi.NewRouter()
	r.Route("/api/v1", func(r chi.Router) {
		r.Route("/users/me", func(r chi.Router) {
			r.Use(middleware.JWTAuth(testConfig.JWT))
			userEloRepo := repository.NewUserEloRepository(testutils.GetFakeDB(t).DB)
			userSprintDailyStatsRepo := repository.NewUserSprintDailyStatsRepository(testutils.GetFakeDB(t).DB)

			// User handler for puzzle attempts
			userLearningDailyStatsRepo := fake.NewFakeUserLearningDailyStatsRepository(testutils.GetFakeDB(t))
			learningQueueRepo := fake.NewFakeLearningQueueRepository(testutils.GetFakeDB(t), userLearningDailyStatsRepo)
			hUser := NewUserHandler(userRepo, gameRepo, puzzleRepo, taskRepo, puzzleQueueRepo, learningQueueRepo, userDailyStatsRepo, userEloRepo, userSprintDailyStatsRepo, chessVerifier, eventService)
			r.Post("/puzzles/{puzzleID}/attempts", hUser.PostPuzzleAttempt)

			// Puzzle queue endpoints
			r.Mount("/puzzle-queue", PuzzleQueueRoutes(puzzleQueueRepo, puzzleRepo))
		})
	})

	return userRepo, puzzleRepo, puzzleQueueRepo, r
}

func createTestPuzzleWithTheme(t *testing.T, puzzleRepo repository.IPuzzleRepository, gameID, userID string, theme models.PuzzleTheme) *models.Puzzle {
	puzzle := &models.Puzzle{
		GameID:      gameID,
		UserID:      userID,
		GameMove:    15,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       pq.StringArray{"e2e4", "e7e5"},
		PrevCP:      -50,
		CP:          150,
		Theme:       theme,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    false,
		Tags:        pq.StringArray{"tactics", "test"},
	}
	err := puzzleRepo.Create(context.TODO(), puzzle)
	require.NoError(t, err)
	return puzzle
}

func TestPuzzleQueueAPI(t *testing.T) {
	userRepo, puzzleRepo, puzzleQueueRepo, router := setupPuzzleQueueTest(t)

	// Create a test user
	testUser := testutils.CreateTestUser(t, userRepo)

	// Create a test game first (required for puzzles)
	gameRepo := fake.NewGameRepository(fake.NewDB(t))
	pgn := "1. e4 e5 2. Nf3 Nc6"
	compressedPGN, err := utils.CompressPGN(pgn)
	require.NoError(t, err)

	url := "https://chess.com/game/123"
	testGame := &models.Game{
		UserID:        testUser.ID,
		Platform:      models.ChessDotCom,
		ChessUsername: "testuser",
		UserColor:     models.White,
		CompressedPGN: compressedPGN,
		TimeControl:   "5+0",
		Rated:         true,
		URL:           &url,
		WhitePlayer:   "testuser",
		BlackPlayer:   "opponent",
		Winner:        models.WinnerWhite,
		Result:        models.Mate,
	}
	err = gameRepo.Create(context.TODO(), testGame)
	require.NoError(t, err)

	// Create test puzzles with different themes
	opponentPuzzle := createTestPuzzleWithTheme(t, puzzleRepo, testGame.ID, testUser.ID, models.OpponentMistakeMissed)
	ownPuzzle := createTestPuzzleWithTheme(t, puzzleRepo, testGame.ID, testUser.ID, models.OwnMistakePunished)
	_ = createTestPuzzleWithTheme(t, puzzleRepo, testGame.ID, testUser.ID, models.OpponentMistakeCaught) // Should not be queued

	t.Run("AddPuzzlesToQueue_Success", func(t *testing.T) {
		// Create add puzzles request
		addReq := AddPuzzlesToQueueRequest{
			Count: 10,
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzle-queue/add", addReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response AddPuzzlesToQueueResponse
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		require.NoError(t, err)

		// The fake implementation returns mock puzzle IDs, so we expect some puzzles to be added
		// The exact count depends on the fake implementation
		assert.GreaterOrEqual(t, response.AddedCount, 0)
		assert.GreaterOrEqual(t, response.SkippedCount, 0)
	})

	t.Run("AddPuzzlesToQueue_WithMistakeByFilter", func(t *testing.T) {
		// Clear queue first
		err := puzzleQueueRepo.RemoveFromQueue(context.TODO(), testUser.ID, opponentPuzzle.ID)
		require.NoError(t, err)
		err = puzzleQueueRepo.RemoveFromQueue(context.TODO(), testUser.ID, ownPuzzle.ID)
		require.NoError(t, err)

		// Create add puzzles request with opponent filter
		mistakeBy := "opponent"
		addReq := AddPuzzlesToQueueRequest{
			Count:     10,
			MistakeBy: &mistakeBy,
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzle-queue/add", addReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response AddPuzzlesToQueueResponse
		err = json.Unmarshal(resp.Body.Bytes(), &response)
		require.NoError(t, err)

		// The fake implementation returns mock puzzle IDs
		assert.GreaterOrEqual(t, response.AddedCount, 0)
	})

	t.Run("AddPuzzlesToQueue_InvalidCount", func(t *testing.T) {
		// Create add puzzles request with invalid count
		addReq := AddPuzzlesToQueueRequest{
			Count: 250, // Exceeds max of 200
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzle-queue/add", addReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("GetDuePuzzles_Success", func(t *testing.T) {
		// Add puzzles to queue first
		addReq := AddPuzzlesToQueueRequest{Count: 10}
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzle-queue/add", addReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Get due puzzles
		req = testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/puzzle-queue/due", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp = testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response map[string]interface{}
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		require.NoError(t, err)

		puzzles, ok := response["puzzles"].([]interface{})
		require.True(t, ok)
		// The fake implementation may return 0 puzzles if none are due
		assert.GreaterOrEqual(t, len(puzzles), 0)
	})

	t.Run("GetDuePuzzles_WithMistakeByFilter", func(t *testing.T) {
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		// Get due puzzles filtered by opponent mistakes
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/puzzle-queue/due?mistake_by=opponent", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response map[string]interface{}
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		require.NoError(t, err)

		puzzles, ok := response["puzzles"].([]interface{})
		require.True(t, ok)

		// All returned puzzles should be opponent mistakes
		for _, p := range puzzles {
			puzzle := p.(map[string]interface{})
			assert.Equal(t, "opponent", puzzle["mistake_by"])
		}
	})

	t.Run("GetQueueStats_Success", func(t *testing.T) {
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/puzzle-queue/stats", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var stats models.PuzzleQueueStats
		err := json.Unmarshal(resp.Body.Bytes(), &stats)
		require.NoError(t, err)

		assert.GreaterOrEqual(t, stats.TotalQueued, int64(0))
		assert.GreaterOrEqual(t, stats.DueToday, int64(0))
		assert.GreaterOrEqual(t, stats.OpponentMistakes, int64(0))
		assert.GreaterOrEqual(t, stats.OwnMistakes, int64(0))
	})

	t.Run("PuzzleAttempt_WithQueueUpdate", func(t *testing.T) {
		// Create puzzle attempt request with queue flag
		attemptReq := PostPuzzleAttemptRequest{
			Moves:          []string{"Qh5", "g6", "Qxf7#"},
			Solved:         true,
			TimeSpent:      45,
			AttemptType:    "normal_solve",
			IsQueueAttempt: true,
		}

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/users/me/puzzles/"+opponentPuzzle.ID+"/attempts", attemptReq)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Verify queue entry was updated (this would require checking the queue state)
		// For now, just verify the request was processed successfully
	})

	t.Run("Unauthorized_Access", func(t *testing.T) {
		// Try to access queue endpoints without authentication
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/puzzle-queue/due", nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})
}

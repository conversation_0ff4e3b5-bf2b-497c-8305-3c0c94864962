package api

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupLearningQueueTest(t *testing.T) (repository.IUserRepository, repository.ILearningQueueRepository, repository.IUserLearningDailyStatsRepository, http.Handler) {
	// Create a fake DB for testing
	db := fake.NewDB(t)

	// Create repositories
	userRepo := fake.NewUserRepository(db)
	userLearningDailyStatsRepo := fake.NewFakeUserLearningDailyStatsRepository(db)
	learningQueueRepo := fake.NewFakeLearningQueueRepository(db, userLearningDailyStatsRepo)

	// Create test config
	jwtConfig := testutils.GetTestJWTConfig()
	testConfig := &config.Config{
		JWT: jwtConfig,
	}

	// Create the router
	r := chi.NewRouter()
	r.Route("/api/v1", func(r chi.Router) {
		r.Route("/users/me", func(r chi.Router) {
			r.Use(middleware.JWTAuth(testConfig.JWT))

			// Learning queue endpoints
			r.Mount("/learning-queue", LearningQueueRoutes(learningQueueRepo))
		})
	})

	return userRepo, learningQueueRepo, userLearningDailyStatsRepo, r
}

func TestLearningQueueAPI(t *testing.T) {
	userRepo, learningQueueRepo, _, router := setupLearningQueueTest(t)

	// Create a test user
	testUser := &models.User{
		Email:        "<EMAIL>",
		PasswordHash: "hashedpassword",
	}
	err := userRepo.Create(context.Background(), testUser)
	require.NoError(t, err)

	t.Run("GetDuePuzzles_EmptyQueue", func(t *testing.T) {
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response GetDuePuzzlesResponse
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Empty(t, response.Puzzles)
		assert.Equal(t, int64(0), response.TotalDue)
	})

	t.Run("GetQueueStats_EmptyQueue", func(t *testing.T) {
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/stats", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var stats models.LearningQueueStats
		err := json.Unmarshal(resp.Body.Bytes(), &stats)
		require.NoError(t, err)

		assert.Equal(t, int64(0), stats.TotalQueued)
		assert.Equal(t, int64(0), stats.DueToday)
		assert.Equal(t, int64(0), stats.RegularPuzzles)
		assert.Equal(t, int64(0), stats.ArrowDuelPuzzles)
	})

	t.Run("GetDuePuzzles_WithPuzzles", func(t *testing.T) {
		// Add some puzzles to the learning queue
		now := time.Now()
		puzzles := []models.LearningQueueEntry{
			{
				ID:                uuid.New().String(),
				UserID:            testUser.ID,
				LichessPuzzleID:   "puzzle-due-1",
				FailedAttemptType: "regular",
				DueAt:             now.Add(-1 * time.Hour), // Due 1 hour ago
				OriginalSprintID:  "sprint-1",
			},
			{
				ID:                uuid.New().String(),
				UserID:            testUser.ID,
				LichessPuzzleID:   "puzzle-due-2",
				FailedAttemptType: "arrow_duel",
				DueAt:             now.Add(-30 * time.Minute), // Due 30 minutes ago
				OriginalSprintID:  "sprint-1",
			},
			{
				ID:                uuid.New().String(),
				UserID:            testUser.ID,
				LichessPuzzleID:   "puzzle-future",
				FailedAttemptType: "regular",
				DueAt:             now.Add(1 * time.Hour), // Due in 1 hour
				OriginalSprintID:  "sprint-1",
			},
		}

		_, err := learningQueueRepo.AddPuzzlesToQueue(context.Background(), testUser.ID, puzzles)
		require.NoError(t, err)

		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		// Test getting all due puzzles
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response GetDuePuzzlesResponse
		err = json.Unmarshal(resp.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Len(t, response.Puzzles, 2) // Should return 2 due puzzles
		assert.Equal(t, int64(2), response.TotalDue)

		// Verify puzzle data structure
		for _, puzzle := range response.Puzzles {
			assert.NotEmpty(t, puzzle.QueueID)
			assert.NotEmpty(t, puzzle.LichessPuzzleID)
			assert.Contains(t, []string{"regular", "arrow_duel"}, puzzle.FailedAttemptType)
			assert.NotNil(t, puzzle.PuzzleData)
			assert.NotEmpty(t, puzzle.PuzzleData.ID)
		}
	})

	t.Run("GetDuePuzzles_WithAttemptTypeFilter", func(t *testing.T) {
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		// Test filtering by regular puzzles
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due?attempt_type=regular", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response GetDuePuzzlesResponse
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Len(t, response.Puzzles, 1) // Should return 1 regular puzzle
		assert.Equal(t, "regular", response.Puzzles[0].FailedAttemptType)

		// Test filtering by arrow_duel puzzles
		req = testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due?attempt_type=arrow_duel", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp = testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		err = json.Unmarshal(resp.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Len(t, response.Puzzles, 1) // Should return 1 arrow_duel puzzle
		assert.Equal(t, "arrow_duel", response.Puzzles[0].FailedAttemptType)
	})

	t.Run("GetDuePuzzles_WithLimit", func(t *testing.T) {
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		// Test with limit=1
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due?limit=1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response GetDuePuzzlesResponse
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Len(t, response.Puzzles, 1)           // Should return only 1 puzzle
		assert.Equal(t, int64(2), response.TotalDue) // But total due should still be 2
	})

	t.Run("GetQueueStats_WithPuzzles", func(t *testing.T) {
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/stats", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var stats models.LearningQueueStats
		err := json.Unmarshal(resp.Body.Bytes(), &stats)
		require.NoError(t, err)

		assert.Equal(t, int64(3), stats.TotalQueued)      // 3 total puzzles
		assert.Equal(t, int64(2), stats.DueToday)         // 2 due puzzles
		assert.Equal(t, int64(2), stats.RegularPuzzles)   // 2 regular puzzles (1 due + 1 future)
		assert.Equal(t, int64(1), stats.ArrowDuelPuzzles) // 1 arrow_duel puzzle

		// These are mock values from the fake implementation
		assert.Equal(t, int64(5), stats.DailyRetriesToday)
		assert.Equal(t, int64(23), stats.DailyRetriesThisWeek)
		assert.Equal(t, 0.65, stats.MasteryRate)
	})

	t.Run("ErrorCases", func(t *testing.T) {
		token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

		// Test invalid attempt_type
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due?attempt_type=invalid", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)

		// Test high limit (should be capped to 50, not return error)
		req = testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due?limit=100", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp = testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code) // Should succeed, limit capped to 50

		// Test negative limit (should use default limit, not return error)
		req = testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due?limit=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp = testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code) // Should succeed, uses default limit
	})

	t.Run("Unauthorized", func(t *testing.T) {
		// Test without authorization header
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due", nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)

		// Test stats endpoint without authorization
		req = testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/stats", nil)
		resp = testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})
}

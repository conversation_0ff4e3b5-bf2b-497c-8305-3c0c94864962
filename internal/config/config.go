package config

import (
	"fmt"
	"net/url"
	"os"
	"strings"

	"github.com/joho/godotenv"
)

type Config struct {
	Server       ServerConfig
	Database     DatabaseConfig
	JWT          JWTConfig
	SessionToken SessionTokenConfig
	Firebase     FirebaseConfig
}

type ServerConfig struct {
	Address string
}

type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

type JWTConfig struct {
	Secret        string
	ExpiryMinutes int
}

type SessionTokenConfig struct {
	ExpiryDays int
}

type FirebaseConfig struct {
	ProjectID             string
	JWKSEndpoint          string
	SkipTokenVerification bool
}

func Load() (*Config, error) {
	// Load .env file if it exists
	_ = godotenv.Load()

	// Get DATABASE_URL
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		return nil, fmt.Errorf("DATABASE_URL environment variable is required")
	}

	// Parse the URL
	parsedURL, err := url.Parse(dbURL)
	if err != nil {
		return nil, fmt.E<PERSON><PERSON>("failed to parse DATABASE_URL: %w", err)
	}

	// Extract user and password
	user := parsedURL.User.Username()
	password, _ := parsedURL.User.Password()

	// Extract host and port
	host := parsedURL.Hostname()
	port := parsedURL.Port()
	if port == "" {
		port = "5432" // Default PostgreSQL port
	}

	// Extract database name (remove leading slash)
	dbName := strings.TrimPrefix(parsedURL.Path, "/")

	// Extract sslmode from query parameters
	sslmode := "require" // Default to require for Heroku compatibility
	if parsedURL.Query().Has("sslmode") {
		sslmode = parsedURL.Query().Get("sslmode")
	}

	jwtSecret := getEnv("JWT_SECRET", "")
	if jwtSecret == "" {
		return nil, fmt.Errorf("JWT_SECRET environment variable is required")
	}

	return &Config{
		Server: ServerConfig{
			Address: ":" + getEnv("PORT", "8080"),
		},
		Database: DatabaseConfig{
			Host:     host,
			Port:     port,
			User:     user,
			Password: password,
			DBName:   dbName,
			SSLMode:  sslmode,
		},
		JWT: JWTConfig{
			Secret:        jwtSecret,
			ExpiryMinutes: getEnvAsInt("JWT_EXPIRY_MINUTES", 60),
		},
		SessionToken: SessionTokenConfig{
			ExpiryDays: getEnvAsInt("SESSION_TOKEN_EXPIRY_DAYS", 30),
		},
		Firebase: FirebaseConfig{
			ProjectID:             getEnv("FIREBASE_PROJECT_ID", "demo-project"),
			JWKSEndpoint:          getEnv("FIREBASE_JWKS_ENDPOINT", "https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>"),
			SkipTokenVerification: getEnvAsBool("FIREBASE_SKIP_TOKEN_VERIFICATION", false),
		},
	}, nil
}

func getEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	valueStr := getEnv(key, "")
	if valueStr == "" {
		return defaultValue
	}

	value := 0
	_, err := fmt.Sscanf(valueStr, "%d", &value)
	if err != nil {
		return defaultValue
	}

	return value
}

func getEnvAsBool(key string, defaultValue bool) bool {
	valueStr := getEnv(key, "")
	if valueStr == "" {
		return defaultValue
	}

	// Accept common boolean representations
	switch strings.ToLower(valueStr) {
	case "true", "1", "yes", "on":
		return true
	case "false", "0", "no", "off":
		return false
	default:
		return defaultValue
	}
}

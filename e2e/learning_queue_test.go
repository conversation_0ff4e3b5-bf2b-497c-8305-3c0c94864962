package e2e

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api"
	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestLearningQueueE2E tests the complete learning queue workflow
func TestLearningQueueE2E(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping E2E tests in short mode")
	}

	// Setup test environment
	db := fake.NewDB(t)
	defer func() {
		if err := db.Close(); err != nil {
			t.Errorf("Failed to close test database: %v", err)
		}
	}()

	// Create repositories
	userRepo := fake.NewUserRepository(db)
	lichessPuzzleRepo := repository.NewLichessPuzzleRepository(db.DB)
	userLearningDailyStatsRepo := fake.NewFakeUserLearningDailyStatsRepository(db)
	learningQueueRepo := fake.NewFakeLearningQueueRepository(db, userLearningDailyStatsRepo)

	// Create test config
	jwtConfig := testutils.GetTestJWTConfig()
	testConfig := &config.Config{JWT: jwtConfig}

	// Create test user
	testUser := &models.User{
		Email:        "<EMAIL>",
		PasswordHash: "hashedpassword",
	}
	err := userRepo.Create(context.Background(), testUser)
	require.NoError(t, err)

	// Create test Lichess puzzles
	puzzles := []*models.LichessPuzzle{
		{
			ID:     "puzzle-1",
			FEN:    "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:  []string{"e2e4", "e7e5"},
			Rating: 1500,
			Themes: []string{"opening"},
		},
		{
			ID:     "puzzle-2",
			FEN:    "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
			Moves:  []string{"d2d4", "exd4"},
			Rating: 1600,
			Themes: []string{"tactics"},
		},
	}

	for _, puzzle := range puzzles {
		err := lichessPuzzleRepo.Save(context.Background(), puzzle)
		require.NoError(t, err)
	}

	// Create router for API testing
	r := chi.NewRouter()
	r.Route("/api/v1", func(r chi.Router) {
		r.Route("/users/me", func(r chi.Router) {
			r.Use(middleware.JWTAuth(testConfig.JWT))

			// Learning queue endpoints
			r.Mount("/learning-queue", api.LearningQueueRoutes(learningQueueRepo))
		})
	})

	// Generate token for authenticated requests
	token := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

	t.Run("CompleteWorkflow", func(t *testing.T) {
		// Step 1: Manually add puzzles to learning queue (simulating failed sprint)
		queueEntries := []models.LearningQueueEntry{
			{
				ID:                uuid.New().String(),
				UserID:            testUser.ID,
				LichessPuzzleID:   "puzzle-1",
				FailedAttemptType: "regular",
				DueAt:             time.Now(),
				OriginalSprintID:  "test-sprint-1",
			},
			{
				ID:                uuid.New().String(),
				UserID:            testUser.ID,
				LichessPuzzleID:   "puzzle-2",
				FailedAttemptType: "regular",
				DueAt:             time.Now(),
				OriginalSprintID:  "test-sprint-1",
			},
		}

		_, err := learningQueueRepo.AddPuzzlesToQueue(context.Background(), testUser.ID, queueEntries)
		require.NoError(t, err)

		// Step 2: Check that puzzles are now in the learning queue
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, r, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var duePuzzlesResp api.GetDuePuzzlesResponse
		err = json.Unmarshal(resp.Body.Bytes(), &duePuzzlesResp)
		require.NoError(t, err)

		assert.Len(t, duePuzzlesResp.Puzzles, 2, "Both failed puzzles should be in learning queue")
		assert.Equal(t, int64(2), duePuzzlesResp.TotalDue)

		// Verify puzzle data
		for _, queueItem := range duePuzzlesResp.Puzzles {
			assert.Equal(t, "regular", queueItem.FailedAttemptType)
			assert.Equal(t, 0, queueItem.AttemptsSinceAdded)
			assert.Equal(t, 0, queueItem.ConsecutiveCorrect)
			assert.NotNil(t, queueItem.PuzzleData)
		}

		// Step 3: Check learning queue stats
		req = testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/stats", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp = testutils.ExecuteRequest(t, r, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var stats models.LearningQueueStats
		err = json.Unmarshal(resp.Body.Bytes(), &stats)
		require.NoError(t, err)

		assert.Equal(t, int64(2), stats.TotalQueued)
		assert.Equal(t, int64(2), stats.DueToday)
		assert.Equal(t, int64(2), stats.RegularPuzzles)
		assert.Equal(t, int64(0), stats.ArrowDuelPuzzles)

		// Step 4: Practice puzzles from learning queue (simulate correct attempts)
		for i, queueItem := range duePuzzlesResp.Puzzles {
			// Simulate 5 consecutive correct attempts to master the puzzle
			for attempt := 1; attempt <= 5; attempt++ {
				err := learningQueueRepo.UpdateAfterAttempt(
					context.Background(),
					testUser.ID,
					queueItem.LichessPuzzleID,
					true, // correct
					"regular",
					45, // time spent
				)
				require.NoError(t, err)

				// Check if puzzle is still in queue after each attempt
				exists, err := learningQueueRepo.ExistsInQueue(context.Background(), testUser.ID, queueItem.LichessPuzzleID)
				require.NoError(t, err)

				if attempt < 5 {
					assert.True(t, exists, "Puzzle should still be in queue after %d correct attempts", attempt)
				} else {
					assert.False(t, exists, "Puzzle should be removed from queue after 5 correct attempts (mastered)")
				}
			}

			// Verify the puzzle is mastered and removed from queue
			req = testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/due", nil)
			req.Header.Set("Authorization", "Bearer "+token)
			resp = testutils.ExecuteRequest(t, r, req)

			testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

			err = json.Unmarshal(resp.Body.Bytes(), &duePuzzlesResp)
			require.NoError(t, err)

			expectedRemaining := len(puzzles) - (i + 1)
			assert.Len(t, duePuzzlesResp.Puzzles, expectedRemaining, "Should have %d puzzles remaining after mastering puzzle %d", expectedRemaining, i+1)
		}

		// Step 5: Verify learning queue is now empty
		req = testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me/learning-queue/stats", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp = testutils.ExecuteRequest(t, r, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		err = json.Unmarshal(resp.Body.Bytes(), &stats)
		require.NoError(t, err)

		assert.Equal(t, int64(0), stats.TotalQueued, "Learning queue should be empty after mastering all puzzles")
		assert.Equal(t, int64(0), stats.DueToday)
	})

	t.Run("SpacedRepetitionWorkflow", func(t *testing.T) {
		// Create another puzzle for spaced repetition testing
		spacedPuzzle := &models.LichessPuzzle{
			ID:     "puzzle-spaced",
			FEN:    "r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3",
			Moves:  []string{"Bb5", "a6"},
			Rating: 1400,
			Themes: []string{"pin"},
		}
		err := lichessPuzzleRepo.Save(context.Background(), spacedPuzzle)
		require.NoError(t, err)

		// Add puzzle to learning queue manually
		queueEntry := models.LearningQueueEntry{
			ID:                uuid.New().String(),
			UserID:            testUser.ID,
			LichessPuzzleID:   spacedPuzzle.ID,
			FailedAttemptType: "regular",
			DueAt:             time.Now(),
			OriginalSprintID:  "test-sprint",
		}

		_, err = learningQueueRepo.AddPuzzlesToQueue(context.Background(), testUser.ID, []models.LearningQueueEntry{queueEntry})
		require.NoError(t, err)

		// Test spaced repetition intervals
		intervals := []time.Duration{
			2 * 24 * time.Hour,  // 2 days after 1st correct
			4 * 24 * time.Hour,  // 4 days after 2nd correct
			7 * 24 * time.Hour,  // 7 days after 3rd correct
			15 * 24 * time.Hour, // 15 days after 4th correct
		}

		for i, expectedInterval := range intervals {
			// Submit correct attempt
			err := learningQueueRepo.UpdateAfterAttempt(
				context.Background(),
				testUser.ID,
				spacedPuzzle.ID,
				true, // correct
				"regular",
				60,
			)
			require.NoError(t, err)

			// Check the puzzle's due date
			entry, err := learningQueueRepo.GetByUserIDAndPuzzleID(context.Background(), testUser.ID, spacedPuzzle.ID)
			require.NoError(t, err)

			expectedDueAt := time.Now().Add(expectedInterval)
			timeDiff := entry.DueAt.Sub(expectedDueAt)
			assert.True(t, timeDiff < time.Hour && timeDiff > -time.Hour,
				"Expected due date around %v, got %v (diff: %v) for attempt %d", expectedDueAt, entry.DueAt, timeDiff, i+1)

			assert.Equal(t, i+1, entry.ConsecutiveCorrect, "Should have %d consecutive correct attempts", i+1)
		}

		// Test incorrect attempt (should reset spaced repetition)
		err = learningQueueRepo.UpdateAfterAttempt(
			context.Background(),
			testUser.ID,
			spacedPuzzle.ID,
			false, // incorrect
			"regular",
			45,
		)
		require.NoError(t, err)

		// Check that consecutive correct was reset
		entry, err := learningQueueRepo.GetByUserIDAndPuzzleID(context.Background(), testUser.ID, spacedPuzzle.ID)
		require.NoError(t, err)

		assert.Equal(t, 0, entry.ConsecutiveCorrect, "Consecutive correct should be reset after incorrect attempt")

		// Due date should be approximately 24 hours from now
		expectedDueAt := time.Now().Add(24 * time.Hour)
		timeDiff := entry.DueAt.Sub(expectedDueAt)
		assert.True(t, timeDiff < time.Hour && timeDiff > -time.Hour,
			"After incorrect attempt, should be due in ~24 hours. Expected: %v, Got: %v", expectedDueAt, entry.DueAt)
	})
}

package e2e

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	firebaseEmulator "github.com/chessticize/chessticize-server/internal/testing"
	"github.com/stretchr/testify/require"
)

// randomEmail generates a unique email for testing
func randomEmail() string {
	return fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
}

func TestFirebaseAuthenticationE2E(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Find an available port to avoid conflicts
	port, err := firebaseEmulator.FindAvailablePort(9099)
	require.NoError(t, err, "Failed to find available port")
	portStr := fmt.Sprintf("%d", port)
	t.Logf("Using port %s for Firebase emulator", portStr)

	// Setup Firebase emulator
	emulator, err := firebaseEmulator.NewFirebaseEmulator("demo-project")
	require.NoError(t, err)

	// Start Firebase emulator server
	server := emulator.StartServer(portStr)
	defer func() {
		if err := server.Close(); err != nil {
			t.Logf("Failed to close server: %v", err)
		}
	}()

	// Verify the JWKS endpoint is working
	jwksURL := fmt.Sprintf("http://localhost:%s/.well-known/jwks.json", portStr)
	resp, err := http.Get(jwksURL)
	require.NoError(t, err, "Failed to reach JWKS endpoint")
	defer resp.Body.Close()
	require.Equal(t, http.StatusOK, resp.StatusCode, "JWKS endpoint not responding correctly")

	// Log the JWKS response for debugging
	var jwksResp map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&jwksResp)
	require.NoError(t, err, "Failed to decode JWKS response")
	t.Logf("JWKS response: %+v", jwksResp)

	t.Run("Firebase_NewUser_Registration", func(t *testing.T) {
		// Generate test Firebase token with unique email using email_link provider
		userID := "firebase-user-e2e-123"
		email := randomEmail()
		firebaseToken, err := emulator.GenerateTestTokenWithProvider(userID, email, "email_link")
		require.NoError(t, err)

		// Exchange Firebase token for our JWT
		payload := map[string]string{
			"firebase_token": firebaseToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/auth/firebase-exchange", nil, payload)
		defer closeResponseBody(t, resp)

		// Should create new user (201)
		require.Equal(t, http.StatusCreated, resp.StatusCode, "Firebase token exchange failed. Response body: %s", readResponseBody(t, resp))

		// Parse response
		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		// Verify response structure
		require.Contains(t, result, "token")
		require.Contains(t, result, "user")
		// Note: session_token is not returned for Firebase authentication

		user := result["user"].(map[string]interface{})
		require.Equal(t, email, user["email"])
		require.Equal(t, userID, user["firebase_uid"])

		// Verify we can use the returned JWT token
		token := result["token"].(string)
		profile := getMyProfile(t, token)
		require.Equal(t, email, profile["email"])
		require.Equal(t, userID, profile["firebase_uid"])
	})

	t.Run("Firebase_ExistingUser_Login", func(t *testing.T) {
		// First, create a user with regular registration
		email := randomEmail()
		password := "testpassword123"
		_, _ = registerPublic(t, email, password)

		// Now try to link Firebase account with same email using email_link provider
		userID := "firebase-user-e2e-456"
		firebaseToken, err := emulator.GenerateTestTokenWithProvider(userID, email, "email_link")
		require.NoError(t, err)

		// Exchange Firebase token for our JWT
		payload := map[string]string{
			"firebase_token": firebaseToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/auth/firebase-exchange", nil, payload)
		defer closeResponseBody(t, resp)

		// Should link to existing user (200, not 201)
		require.Equal(t, http.StatusOK, resp.StatusCode, "Firebase token exchange failed. Response body: %s", readResponseBody(t, resp))

		// Parse response
		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		// Verify Firebase UID was linked
		user := result["user"].(map[string]interface{})
		require.Equal(t, email, user["email"])
		require.Equal(t, userID, user["firebase_uid"])

		// Verify we can use the returned JWT token
		token := result["token"].(string)
		profile := getMyProfile(t, token)
		require.Equal(t, email, profile["email"])
		require.Equal(t, userID, profile["firebase_uid"])
	})

	t.Run("Firebase_InvalidToken_Rejection", func(t *testing.T) {
		// Try with invalid token
		payload := map[string]string{
			"firebase_token": "invalid-token-string",
		}

		resp := makeRequest(t, "POST", "/api/v1/auth/firebase-exchange", nil, payload)
		defer closeResponseBody(t, resp)

		// Should reject invalid token
		require.Equal(t, http.StatusUnauthorized, resp.StatusCode, "Expected invalid token to be rejected")
	})

	t.Run("Firebase_EmptyToken_Rejection", func(t *testing.T) {
		// Try with empty token
		payload := map[string]string{
			"firebase_token": "",
		}

		resp := makeRequest(t, "POST", "/api/v1/auth/firebase-exchange", nil, payload)
		defer closeResponseBody(t, resp)

		// Should reject empty token
		require.Equal(t, http.StatusBadRequest, resp.StatusCode, "Expected empty token to be rejected")
	})

}
